[2025-06-02 15:09:44] lcoal.ERROR: Method Illuminate\Auth\RequestGuard::login does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Auth\\RequestGuard::login does not exist. at C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\RequestGuard->__call('login', Array)
#1 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(24): Illuminate\\Auth\\AuthManager->__call('login', Array)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
[2025-06-02 15:11:00] lcoal.ERROR: Method Illuminate\Auth\RequestGuard::login does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Auth\\RequestGuard::login does not exist. at C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\RequestGuard->__call('login', Array)
#1 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(24): Illuminate\\Auth\\AuthManager->__call('login', Array)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
[2025-06-02 15:11:23] lcoal.ERROR: Method Illuminate\Auth\RequestGuard::login does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Auth\\RequestGuard::login does not exist. at C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(24): Illuminate\\Auth\\RequestGuard->__call('login', Array)
#1 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#28 {main}
"} 
[2025-06-02 15:18:22] lcoal.ERROR: Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  "error": "invalid_request",
  "error_description": "Invalid Credentials"
}
 {"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  \"error\": \"invalid_request\",
  \"error_description\": \"Invalid Credentials\"
}
 at C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:111)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://www.goo...', Array)
#11 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\google\\Provider.php(54): GuzzleHttp\\Client->get('https://www.goo...', Array)
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\socialite\\src\\Two\\AbstractProvider.php(272): SocialiteProviders\\Google\\Provider->getUserByToken('a0AW4XtxjGzEDIe...')
#13 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(22): Laravel\\Socialite\\Two\\AbstractProvider->userFromToken('a0AW4XtxjGzEDIe...')
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 {main}
"} 
[2025-06-02 16:12:33] lcoal.ERROR: Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  "error": "invalid_request",
  "error_description": "Invalid Credentials"
}
 {"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  \"error\": \"invalid_request\",
  \"error_description\": \"Invalid Credentials\"
}
 at C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:111)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://www.goo...', Array)
#11 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\google\\Provider.php(54): GuzzleHttp\\Client->get('https://www.goo...', Array)
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\socialite\\src\\Two\\AbstractProvider.php(272): SocialiteProviders\\Google\\Provider->getUserByToken('0AW4XtxjGzEDIeT...')
#13 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(22): Laravel\\Socialite\\Two\\AbstractProvider->userFromToken('0AW4XtxjGzEDIeT...')
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 {main}
"} 
[2025-06-02 16:39:20] lcoal.ERROR: Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  "error": "invalid_request",
  "error_description": "Invalid Credentials"
}
 {"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  \"error\": \"invalid_request\",
  \"error_description\": \"Invalid Credentials\"
}
 at C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:111)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://www.goo...', Array)
#11 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\google\\Provider.php(54): GuzzleHttp\\Client->get('https://www.goo...', Array)
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\socialite\\src\\Two\\AbstractProvider.php(272): SocialiteProviders\\Google\\Provider->getUserByToken('0AW4XtxjGzEDIeT...')
#13 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(22): Laravel\\Socialite\\Two\\AbstractProvider->userFromToken('0AW4XtxjGzEDIeT...')
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 {main}
"} 
[2025-06-03 11:08:38] lcoal.ERROR: Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  "error": "invalid_request",
  "error_description": "Invalid Credentials"
}
 {"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  \"error\": \"invalid_request\",
  \"error_description\": \"Invalid Credentials\"
}
 at C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:111)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://www.goo...', Array)
#11 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\google\\Provider.php(54): GuzzleHttp\\Client->get('https://www.goo...', Array)
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\socialite\\src\\Two\\AbstractProvider.php(272): SocialiteProviders\\Google\\Provider->getUserByToken('AW4Xtxgh4tV-JDJ...')
#13 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(21): Laravel\\Socialite\\Two\\AbstractProvider->userFromToken('AW4Xtxgh4tV-JDJ...')
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 {main}
"} 
[2025-06-03 11:08:55] lcoal.ERROR: Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  "error": "invalid_request",
  "error_description": "Invalid Credentials"
}
 {"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `GET https://www.googleapis.com/oauth2/v3/userinfo` resulted in a `401 Unauthorized` response:
{
  \"error\": \"invalid_request\",
  \"error_description\": \"Invalid Credentials\"
}
 at C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:111)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\laragon\\www\\captainvpn\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php(44): GuzzleHttp\\Client->request('GET', 'https://www.goo...', Array)
#11 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\google\\Provider.php(54): GuzzleHttp\\Client->get('https://www.goo...', Array)
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\socialite\\src\\Two\\AbstractProvider.php(272): SocialiteProviders\\Google\\Provider->getUserByToken('***************...')
#13 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(21): Laravel\\Socialite\\Two\\AbstractProvider->userFromToken('***************...')
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 {main}
"} 
[2025-06-03 13:17:42] lcoal.ERROR: syntax error, unexpected token "}", expecting variable {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\", expecting variable at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:252)
[stacktrace]
#0 {main}
"} 
[2025-06-03 13:17:45] lcoal.ERROR: syntax error, unexpected token "}", expecting variable {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\", expecting variable at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:252)
[stacktrace]
#0 {main}
"} 
[2025-06-03 13:17:51] lcoal.ERROR: syntax error, unexpected token "}", expecting variable {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\", expecting variable at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:252)
[stacktrace]
#0 {main}
"} 
[2025-06-03 13:18:01] lcoal.ERROR: syntax error, unexpected token "}", expecting "(" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\", expecting \"(\" at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:252)
[stacktrace]
#0 {main}
"} 
[2025-06-03 13:19:08] lcoal.ERROR: syntax error, unexpected token "}", expecting ";" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:254)
[stacktrace]
#0 {main}
"} 
[2025-06-03 13:19:38] lcoal.ERROR: syntax error, unexpected token "}", expecting ";" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:254)
[stacktrace]
#0 {main}
"} 
[2025-06-03 13:30:34] lcoal.ERROR: Undefined array key "title_en_US" {"userId":2,"exception":"[object] (ErrorException(code: 0): Undefined array key \"title_en_US\" at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:84)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\laragon\\\\www\\\\...', 84)
#1 C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php(84): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\laragon\\\\www\\\\...', 84)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(700): App\\Models\\Client->getdropdownNameAttribute(NULL)
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(750): Illuminate\\Database\\Eloquent\\Model->mutateAttribute('dropdown_name', NULL)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(230): Illuminate\\Database\\Eloquent\\Model->mutateAttributeForArray('dropdown_name', NULL)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1665): Illuminate\\Database\\Eloquent\\Model->attributesToArray()
#6 [internal function]: Illuminate\\Database\\Eloquent\\Model->Illuminate\\Database\\Eloquent\\{closure}()
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php(46): call_user_func(Object(Closure))
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1664): Illuminate\\Database\\Eloquent\\Model->withoutRecursion(Object(Closure), Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1696): Illuminate\\Database\\Eloquent\\Model->toArray()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1681): Illuminate\\Database\\Eloquent\\Model->jsonSerialize()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(83): Illuminate\\Database\\Eloquent\\Model->toJson(0)
#12 C:\\laragon\\www\\captainvpn\\vendor\\symfony\\http-foundation\\JsonResponse.php(49): Illuminate\\Http\\JsonResponse->setData(Object(App\\Models\\Client))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Object(App\\Models\\Client), 200, Array, false)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\JsonResponse->__construct(Object(App\\Models\\Client))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(App\\Models\\Client))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(App\\Models\\Client))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\ResponseCalls.php(256): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\ResponseCalls.php(249): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\ResponseCalls->callLaravelRoute(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\ResponseCalls.php(86): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\ResponseCalls->makeApiCall(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Strategies\\Responses\\ResponseCalls.php(39): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\ResponseCalls->makeResponseCall(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#43 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(240): Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\ResponseCalls->__invoke(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#44 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(165): Knuckles\\Scribe\\Extracting\\Extractor->iterateThroughStrategies('responses', Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array, Object(Closure))
#45 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Extracting\\Extractor.php(97): Knuckles\\Scribe\\Extracting\\Extractor->fetchResponses(Object(Knuckles\\Camel\\Extraction\\ExtractedEndpointData), Array)
#46 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(125): Knuckles\\Scribe\\Extracting\\Extractor->processRoute(Object(Illuminate\\Routing\\Route), Array)
#47 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(72): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoFromLaravelApp(Array, Array, Array)
#48 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\GroupedEndpoints\\GroupedEndpointsFromApp.php(50): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->extractEndpointsInfoAndWriteToDisk(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), true)
#49 C:\\laragon\\www\\captainvpn\\vendor\\knuckleswtf\\scribe\\src\\Commands\\GenerateDocumentation.php(55): Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFromApp->get()
#50 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Knuckles\\Scribe\\Commands\\GenerateDocumentation->handle(Object(Knuckles\\Scribe\\Matching\\RouteMatcher), Object(Knuckles\\Scribe\\GroupedEndpoints\\GroupedEndpointsFactory))
#51 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#52 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#53 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#54 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#55 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#56 C:\\laragon\\www\\captainvpn\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#57 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#58 C:\\laragon\\www\\captainvpn\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 C:\\laragon\\www\\captainvpn\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Knuckles\\Scribe\\Commands\\GenerateDocumentation), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 C:\\laragon\\www\\captainvpn\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#61 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#62 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#63 C:\\laragon\\www\\captainvpn\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#64 {main}
"} 
[2025-06-03 14:53:14] lcoal.ERROR: syntax error, unexpected token ";", expecting ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \";\", expecting \")\" at C:\\laragon\\www\\captainvpn\\app\\Services\\MobileServices\\AuthService.php:140)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon\\\\www\\\\...')
#1 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\AuthenticationController.php(32): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Services\\\\Mo...')
#2 [internal function]: App\\Http\\Controllers\\MobileControllers\\Auth\\AuthenticationController->__construct()
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1004): ReflectionClass->newInstanceArgs(Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(285): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#28 {main}
"} 
[2025-06-03 16:47:17] lcoal.ERROR: Attempt to read property "device_os" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"device_os\" on null at C:\\laragon\\www\\captainvpn\\app\\Http\\Resources\\SessionResource.php:23)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 23)
#1 C:\\laragon\\www\\captainvpn\\app\\Http\\Resources\\SessionResource.php(23): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 23)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): App\\Http\\Resources\\SessionResource->toArray(Object(Illuminate\\Http\\Request))
#3 [internal function]: Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Http\\Resources\\SessionResource), 0)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(102): Illuminate\\Support\\HigherOrderCollectionProxy->__call('toArray', Array)
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toArray(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php(19): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(134): Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(114): Illuminate\\Http\\Resources\\Json\\ResourceCollection->preparePaginatedResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\captainvpn\\vendor\\spatie\\laravel-permission\\src\\Middleware\\PermissionMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Spatie\\Permission\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'clients/session...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-06-03 16:47:47] lcoal.ERROR: Attempt to read property "device_os" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"device_os\" on null at C:\\laragon\\www\\captainvpn\\app\\Http\\Resources\\SessionResource.php:23)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 23)
#1 C:\\laragon\\www\\captainvpn\\app\\Http\\Resources\\SessionResource.php(23): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 23)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): App\\Http\\Resources\\SessionResource->toArray(Object(Illuminate\\Http\\Request))
#3 [internal function]: Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Http\\Resources\\SessionResource), 0)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(102): Illuminate\\Support\\HigherOrderCollectionProxy->__call('toArray', Array)
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toArray(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php(19): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(134): Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(114): Illuminate\\Http\\Resources\\Json\\ResourceCollection->preparePaginatedResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\captainvpn\\vendor\\spatie\\laravel-permission\\src\\Middleware\\PermissionMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Spatie\\Permission\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'clients/session...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-06-03 16:47:48] lcoal.ERROR: Attempt to read property "device_os" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"device_os\" on null at C:\\laragon\\www\\captainvpn\\app\\Http\\Resources\\SessionResource.php:23)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 23)
#1 C:\\laragon\\www\\captainvpn\\app\\Http\\Resources\\SessionResource.php(23): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 23)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): App\\Http\\Resources\\SessionResource->toArray(Object(Illuminate\\Http\\Request))
#3 [internal function]: Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Http\\Resources\\SessionResource), 0)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(102): Illuminate\\Support\\HigherOrderCollectionProxy->__call('toArray', Array)
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(108): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toArray(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse.php(19): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(134): Illuminate\\Http\\Resources\\Json\\PaginatedResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(114): Illuminate\\Http\\Resources\\Json\\ResourceCollection->preparePaginatedResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(902): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\captainvpn\\vendor\\spatie\\laravel-permission\\src\\Middleware\\PermissionMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Spatie\\Permission\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'clients/session...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-06-03 17:03:56] lcoal.ERROR: App\Models\Client::registerDevice(): Argument #1 ($deviceId) must be of type string, null given, called in C:\laragon\www\captainvpn\app\Http\Controllers\MobileControllers\Auth\SocialLoginController.php on line 23 {"exception":"[object] (TypeError(code: 0): App\\Models\\Client::registerDevice(): Argument #1 ($deviceId) must be of type string, null given, called in C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php on line 23 at C:\\laragon\\www\\captainvpn\\app\\Models\\Client.php:241)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\app\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController.php(23): App\\Models\\Client->registerDevice(NULL)
#1 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController->register(Object(App\\Http\\Requests\\MobileRequests\\SocialLoginRequest))
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('register', Array)
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\MobileControllers\\Auth\\SocialLoginController), 'register')
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\GuardSwitcher.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\GuardSwitcher->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'client')
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\captainvpn\\app\\Http\\Middleware\\Localization.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\captainvpn\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#28 {main}
"} 
[2025-06-04 13:59:27] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:29] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:30] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:30] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:30] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:38] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:38] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:38] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:43] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:43] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 13:59:44] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:00:04] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:00:04] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:01:04] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:01:04] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:12] lcoal.ERROR: require(C:\laragon\www\captainvpn\routes/vpn.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(C:\\laragon\\www\\captainvpn\\routes/vpn.php): Failed to open stream: No such file or directory at C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(C:\\\\lara...', 'C:\\\\laragon\\\\www\\\\...', 35)
#1 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(C:\\\\lara...', 'C:\\\\laragon\\\\www\\\\...', 35)
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('C:\\\\laragon\\\\www\\\\...')
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(513): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\laragon\\\\www\\\\...')
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes('C:\\\\laragon\\\\www\\\\...')
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(207): Illuminate\\Routing\\Router->group(Array, 'C:\\\\laragon\\\\www\\\\...')
#6 C:\\laragon\\www\\captainvpn\\bootstrap\\app.php(35): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\laragon\\\\www\\\\...')
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(263): {closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(144): Illuminate\\Container\\Container->call(Object(Closure))
#21 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1154): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#22 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#23 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#24 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#25 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#26 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#27 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#28 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#29 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 {main}
"} 
[2025-06-04 14:02:16] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:22] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:24] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:24] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:24] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:24] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:24] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:42] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:42] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:02:42] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:23] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:23] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:23] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:36] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:36] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:36] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:56] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:56] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:03:56] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:06] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:07] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:16] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:17] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:19] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:20] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:20] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:49] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:59] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:59] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:04:59] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:05:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:05:06] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:06:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:06:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:06:06] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:06:19] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:06:20] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:06:20] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:07:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-04 14:07:05] lcoal.ERROR: SocialiteProviders\Apple\Provider doesn't exist {"exception":"[object] (SocialiteProviders\\Manager\\Exception\\InvalidArgumentException(code: 0): SocialiteProviders\\Apple\\Provider doesn't exist at C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php:176)
[stacktrace]
#0 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\SocialiteWasCalled.php(48): SocialiteProviders\\Manager\\SocialiteWasCalled->classExists('SocialiteProvid...')
#1 C:\\laragon\\www\\captainvpn\\app\\Providers\\AppServiceProvider.php(27): SocialiteProviders\\Manager\\SocialiteWasCalled->extendSocialite('apple', 'SocialiteProvid...')
#2 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Providers\\AppServiceProvider->App\\Providers\\{closure}(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#3 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(287): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('SocialiteProvid...', Array)
#4 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(267): Illuminate\\Events\\Dispatcher->invokeListeners('SocialiteProvid...', Array, false)
#5 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(473): Illuminate\\Events\\Dispatcher->dispatch('SocialiteProvid...')
#6 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(21): event(Object(SocialiteProviders\\Manager\\SocialiteWasCalled))
#7 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1179): SocialiteProviders\\Manager\\ServiceProvider->SocialiteProviders\\Manager\\{closure}(Object(Illuminate\\Foundation\\Application))
#8 C:\\laragon\\www\\captainvpn\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php(18): Illuminate\\Foundation\\Application->booted(Object(Closure))
#9 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): SocialiteProviders\\Manager\\ServiceProvider->boot()
#10 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#15 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(921): Illuminate\\Foundation\\Application->bootProvider(Object(SocialiteProviders\\Manager\\ServiceProvider))
#16 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Foundation\\Application->register(Object(SocialiteProviders\\Manager\\ServiceProvider))
#17 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1014): Illuminate\\Foundation\\Application->registerDeferredProvider('SocialiteProvid...', 'Laravel\\\\Sociali...')
#18 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(990): Illuminate\\Foundation\\Application->loadDeferredProvider('Laravel\\\\Sociali...')
#19 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(477): Illuminate\\Foundation\\Application->loadDeferredProviders()
#20 C:\\laragon\\www\\captainvpn\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
