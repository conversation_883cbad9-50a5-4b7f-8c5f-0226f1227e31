<?php

namespace App\Http\Controllers\MobileControllers\Auth;

use App\Enums\ClientTypesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\SocialLoginRequest;
use App\Models\Client;
use App\Services\Mobile\SocialLoginService;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use GuzzleHttp\Exception\ClientException;

class SocialLoginController extends Controller
{

    public function register(SocialLoginRequest $request)
    {
        $provider = $request->provider;
        $token = $request->token;

        if('apple'==$provider)
        config()->set('services.apple.client_secret', (new SocialLoginService())->generateAppleSecret());

        $user = Socialite::driver($provider)->stateless()->userFromToken($token);
        $client = $this->storeClient($provider, $user);
        $device = $client->registerDevice($request->validated('device_id'));
        $res = $client->tokenize($device->id);
        return self::jsonResponse('success', $res);
    }

    protected function storeClient($provider, $user)
    {
        $client = Client::updateOrCreate([
            'email' => $user->getEmail(),
        ], [
            'type' => ClientTypesEnum::FREE->value,
        ]);
        
        $client->update(['username' => $client->id . Str::random(3) ]);

        $client->ExternalLogins()->updateOrCreate([
            'provider' => $provider,
            'provider_id' => $user->getId()
        ]);
        return $client;
    }
}
