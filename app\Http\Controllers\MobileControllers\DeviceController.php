<?php

namespace App\Http\Controllers\MobileControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\SaveDeviceInfoRequest;
use App\Models\Device;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DeviceController extends Controller
{
    public function saveDeviceInfo(SaveDeviceInfoRequest $request)
    {
        $input = $request->validated();
        $input['last_connection_at'] = Carbon::now();

        Auth::user()
            ->currentAccessToken()
            ->device()
            ->update($input);

        return self::MobileResponse('success', []);
    }
}
