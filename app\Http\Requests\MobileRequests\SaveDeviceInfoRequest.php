<?php

namespace App\Http\Requests\MobileRequests;

use App\Http\Requests\BaseRequest;
use Illuminate\Foundation\Http\FormRequest;

class SaveDeviceInfoRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'device_os' => ['required', 'string', 'max:50'],
            'device_os_version' => ['required', 'string', 'max:10'],
            'app_version' => ['required', 'string', 'max:10'],
            'fcm_token' => ['required', 'string', 'max:255']
        ];
    }
}
