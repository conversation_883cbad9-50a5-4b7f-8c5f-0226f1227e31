{
    "_readme": [
        "This file locks the dependencies of your project to a known state",
        "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies",
        "This file is @generated automatically"
    ],
<<<<<<< HEAD
    "content-hash": "9145a54518d7838f1e27da11fe81a371",
=======
    "content-hash": "144be115a9b006aee2af5696e91b8314",
>>>>>>> 8d3461b4ca66c19e1163108f7782a88cb5e139b2
    "packages": [
        {
            "name": "algolia/algoliasearch-client-php",
            "version": "4.16.0",
            "source": {
                "type": "git",
                "url": "https://github.com/algolia/algoliasearch-client-php.git",
                "reference": "e751d362b85bd5637be028275b07f4617523ae91"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/algolia/algoliasearch-client-php/zipball/e751d362b85bd5637be028275b07f4617523ae91",
                "reference": "e751d362b85bd5637be028275b07f4617523ae91",
                "shasum": ""
            },
            "require": {
                "ext-curl": "*",
                "ext-json": "*",
                "ext-mbstring": "*",
                "guzzlehttp/psr7": "^2.0",
                "php": ">=8.1 !=8.3.0",
                "psr/http-message": "^1.1 || ^2.0",
                "psr/log": "^1.0 || ^2.0 || ^3.0",
                "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"
            },
            "require-dev": {
                "friendsofphp/php-cs-fixer": "^2.0 || ^3.5.0",
                "phpstan/phpstan": "^1.12",
                "phpunit/phpunit": "^11.0",
                "vlucas/phpdotenv": "^5.4"
            },
            "suggest": {
                "guzzlehttp/guzzle": "If you prefer to use Guzzle HTTP client instead of the Http Client implementation provided by the package"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "lib/Http/Psr7/functions.php"
                ],
                "psr-4": {
                    "Algolia\\AlgoliaSearch\\": "lib/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Algolia Team",
                    "homepage": "https://alg.li/support"
                }
            ],
            "description": "API powering the features of Algolia.",
            "homepage": "https://github.com/algolia/algoliasearch-client-php",
            "keywords": [
                "algolia",
                "api",
                "client",
                "php",
                "search"
            ],
            "support": {
                "issues": "https://github.com/algolia/algoliasearch-client-php/issues",
                "source": "https://github.com/algolia/algoliasearch-client-php/tree/4.16.0"
            },
            "time": "2025-03-11T21:35:12+00:00"
        },
        {
            "name": "brick/math",
            "version": "0.12.3",
            "source": {
                "type": "git",
                "url": "https://github.com/brick/math.git",
                "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba",
                "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba",
                "shasum": ""
            },
            "require": {
                "php": "^8.1"
            },
            "require-dev": {
                "php-coveralls/php-coveralls": "^2.2",
                "phpunit/phpunit": "^10.1",
                "vimeo/psalm": "6.8.8"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Brick\\Math\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "description": "Arbitrary-precision arithmetic library",
            "keywords": [
                "Arbitrary-precision",
                "BigInteger",
                "BigRational",
                "arithmetic",
                "bigdecimal",
                "bignum",
                "bignumber",
                "brick",
                "decimal",
                "integer",
                "math",
                "mathematics",
                "rational"
            ],
            "support": {
                "issues": "https://github.com/brick/math/issues",
                "source": "https://github.com/brick/math/tree/0.12.3"
            },
            "funding": [
                {
                    "url": "https://github.com/BenMorel",
                    "type": "github"
                }
            ],
            "time": "2025-02-28T13:11:00+00:00"
        },
        {
            "name": "carbonphp/carbon-doctrine-types",
            "version": "3.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git",
                "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d",
                "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d",
                "shasum": ""
            },
            "require": {
                "php": "^8.1"
            },
            "conflict": {
                "doctrine/dbal": "<4.0.0 || >=5.0.0"
            },
            "require-dev": {
                "doctrine/dbal": "^4.0.0",
                "nesbot/carbon": "^2.71.0 || ^3.0.0",
                "phpunit/phpunit": "^10.3"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Carbon\\Doctrine\\": "src/Carbon/Doctrine/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "KyleKatarn",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Types to use Carbon in Doctrine",
            "keywords": [
                "carbon",
                "date",
                "datetime",
                "doctrine",
                "time"
            ],
            "support": {
                "issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues",
                "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"
            },
            "funding": [
                {
                    "url": "https://github.com/kylekatarnls",
                    "type": "github"
                },
                {
                    "url": "https://opencollective.com/Carbon",
                    "type": "open_collective"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/nesbot/carbon",
                    "type": "tidelift"
                }
            ],
            "time": "2024-02-09T16:56:22+00:00"
        },
        {
            "name": "composer/pcre",
            "version": "3.3.2",
            "source": {
                "type": "git",
                "url": "https://github.com/composer/pcre.git",
                "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e",
                "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e",
                "shasum": ""
            },
            "require": {
                "php": "^7.4 || ^8.0"
            },
            "conflict": {
                "phpstan/phpstan": "<1.11.10"
            },
            "require-dev": {
                "phpstan/phpstan": "^1.12 || ^2",
                "phpstan/phpstan-strict-rules": "^1 || ^2",
                "phpunit/phpunit": "^8 || ^9"
            },
            "type": "library",
            "extra": {
                "phpstan": {
                    "includes": [
                        "extension.neon"
                    ]
                },
                "branch-alias": {
                    "dev-main": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Composer\\Pcre\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Jordi Boggiano",
                    "email": "<EMAIL>",
                    "homepage": "http://seld.be"
                }
            ],
            "description": "PCRE wrapping library that offers type-safe preg_* replacements.",
            "keywords": [
                "PCRE",
                "preg",
                "regex",
                "regular expression"
            ],
            "support": {
                "issues": "https://github.com/composer/pcre/issues",
                "source": "https://github.com/composer/pcre/tree/3.3.2"
            },
            "funding": [
                {
                    "url": "https://packagist.com",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/composer",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/composer/composer",
                    "type": "tidelift"
                }
            ],
            "time": "2024-11-12T16:29:46+00:00"
        },
        {
            "name": "composer/semver",
            "version": "3.4.3",
            "source": {
                "type": "git",
                "url": "https://github.com/composer/semver.git",
                "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12",
                "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12",
                "shasum": ""
            },
            "require": {
                "php": "^5.3.2 || ^7.0 || ^8.0"
            },
            "require-dev": {
                "phpstan/phpstan": "^1.11",
                "symfony/phpunit-bridge": "^3 || ^7"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Composer\\Semver\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nils Adermann",
                    "email": "<EMAIL>",
                    "homepage": "http://www.naderman.de"
                },
                {
                    "name": "Jordi Boggiano",
                    "email": "<EMAIL>",
                    "homepage": "http://seld.be"
                },
                {
                    "name": "Rob Bast",
                    "email": "<EMAIL>",
                    "homepage": "http://robbast.nl"
                }
            ],
            "description": "Semver library that offers utilities, version constraint parsing and validation.",
            "keywords": [
                "semantic",
                "semver",
                "validation",
                "versioning"
            ],
            "support": {
                "irc": "ircs://irc.libera.chat:6697/composer",
                "issues": "https://github.com/composer/semver/issues",
                "source": "https://github.com/composer/semver/tree/3.4.3"
            },
            "funding": [
                {
                    "url": "https://packagist.com",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/composer",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/composer/composer",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-19T14:15:21+00:00"
        },
        {
            "name": "dflydev/dot-access-data",
            "version": "v3.0.3",
            "source": {
                "type": "git",
                "url": "https://github.com/dflydev/dflydev-dot-access-data.git",
                "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f",
                "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f",
                "shasum": ""
            },
            "require": {
                "php": "^7.1 || ^8.0"
            },
            "require-dev": {
                "phpstan/phpstan": "^0.12.42",
                "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3",
                "scrutinizer/ocular": "1.6.0",
                "squizlabs/php_codesniffer": "^3.5",
                "vimeo/psalm": "^4.0.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Dflydev\\DotAccessData\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Dragonfly Development Inc.",
                    "email": "<EMAIL>",
                    "homepage": "http://dflydev.com"
                },
                {
                    "name": "Beau Simensen",
                    "email": "<EMAIL>",
                    "homepage": "http://beausimensen.com"
                },
                {
                    "name": "Carlos Frutos",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/cfrutos"
                },
                {
                    "name": "Colin O'Dell",
                    "email": "<EMAIL>",
                    "homepage": "https://www.colinodell.com"
                }
            ],
            "description": "Given a deep data structure, access data by dot notation.",
            "homepage": "https://github.com/dflydev/dflydev-dot-access-data",
            "keywords": [
                "access",
                "data",
                "dot",
                "notation"
            ],
            "support": {
                "issues": "https://github.com/dflydev/dflydev-dot-access-data/issues",
                "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"
            },
            "time": "2024-07-08T12:26:09+00:00"
        },
        {
            "name": "doctrine/inflector",
            "version": "2.0.10",
            "source": {
                "type": "git",
                "url": "https://github.com/doctrine/inflector.git",
                "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc",
                "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc",
                "shasum": ""
            },
            "require": {
                "php": "^7.2 || ^8.0"
            },
            "require-dev": {
                "doctrine/coding-standard": "^11.0",
                "phpstan/phpstan": "^1.8",
                "phpstan/phpstan-phpunit": "^1.1",
                "phpstan/phpstan-strict-rules": "^1.3",
                "phpunit/phpunit": "^8.5 || ^9.5",
                "vimeo/psalm": "^4.25 || ^5.4"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Doctrine\\Inflector\\": "lib/Doctrine/Inflector"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Guilherme Blanco",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Roman Borschel",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Benjamin Eberlei",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jonathan Wage",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Johannes Schmitt",
                    "email": "<EMAIL>"
                }
            ],
            "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.",
            "homepage": "https://www.doctrine-project.org/projects/inflector.html",
            "keywords": [
                "inflection",
                "inflector",
                "lowercase",
                "manipulation",
                "php",
                "plural",
                "singular",
                "strings",
                "uppercase",
                "words"
            ],
            "support": {
                "issues": "https://github.com/doctrine/inflector/issues",
                "source": "https://github.com/doctrine/inflector/tree/2.0.10"
            },
            "funding": [
                {
                    "url": "https://www.doctrine-project.org/sponsorship.html",
                    "type": "custom"
                },
                {
                    "url": "https://www.patreon.com/phpdoctrine",
                    "type": "patreon"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector",
                    "type": "tidelift"
                }
            ],
            "time": "2024-02-18T20:23:39+00:00"
        },
        {
            "name": "doctrine/lexer",
            "version": "3.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/doctrine/lexer.git",
                "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd",
                "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd",
                "shasum": ""
            },
            "require": {
                "php": "^8.1"
            },
            "require-dev": {
                "doctrine/coding-standard": "^12",
                "phpstan/phpstan": "^1.10",
                "phpunit/phpunit": "^10.5",
                "psalm/plugin-phpunit": "^0.18.3",
                "vimeo/psalm": "^5.21"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Doctrine\\Common\\Lexer\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Guilherme Blanco",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Roman Borschel",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Johannes Schmitt",
                    "email": "<EMAIL>"
                }
            ],
            "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.",
            "homepage": "https://www.doctrine-project.org/projects/lexer.html",
            "keywords": [
                "annotations",
                "docblock",
                "lexer",
                "parser",
                "php"
            ],
            "support": {
                "issues": "https://github.com/doctrine/lexer/issues",
                "source": "https://github.com/doctrine/lexer/tree/3.0.1"
            },
            "funding": [
                {
                    "url": "https://www.doctrine-project.org/sponsorship.html",
                    "type": "custom"
                },
                {
                    "url": "https://www.patreon.com/phpdoctrine",
                    "type": "patreon"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer",
                    "type": "tidelift"
                }
            ],
            "time": "2024-02-05T11:56:58+00:00"
        },
        {
            "name": "dragonmantank/cron-expression",
            "version": "v3.4.0",
            "source": {
                "type": "git",
                "url": "https://github.com/dragonmantank/cron-expression.git",
                "reference": "8c784d071debd117328803d86b2097615b457500"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500",
                "reference": "8c784d071debd117328803d86b2097615b457500",
                "shasum": ""
            },
            "require": {
                "php": "^7.2|^8.0",
                "webmozart/assert": "^1.0"
            },
            "replace": {
                "mtdowling/cron-expression": "^1.0"
            },
            "require-dev": {
                "phpstan/extension-installer": "^1.0",
                "phpstan/phpstan": "^1.0",
                "phpunit/phpunit": "^7.0|^8.0|^9.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Cron\\": "src/Cron/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Chris Tankersley",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/dragonmantank"
                }
            ],
            "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due",
            "keywords": [
                "cron",
                "schedule"
            ],
            "support": {
                "issues": "https://github.com/dragonmantank/cron-expression/issues",
                "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"
            },
            "funding": [
                {
                    "url": "https://github.com/dragonmantank",
                    "type": "github"
                }
            ],
            "time": "2024-10-09T13:47:03+00:00"
        },
        {
            "name": "egulias/email-validator",
            "version": "4.0.3",
            "source": {
                "type": "git",
                "url": "https://github.com/egulias/EmailValidator.git",
                "reference": "b115554301161fa21467629f1e1391c1936de517"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/b115554301161fa21467629f1e1391c1936de517",
                "reference": "b115554301161fa21467629f1e1391c1936de517",
                "shasum": ""
            },
            "require": {
                "doctrine/lexer": "^2.0 || ^3.0",
                "php": ">=8.1",
                "symfony/polyfill-intl-idn": "^1.26"
            },
            "require-dev": {
                "phpunit/phpunit": "^10.2",
                "vimeo/psalm": "^5.12"
            },
            "suggest": {
                "ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "4.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Egulias\\EmailValidator\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Eduardo Gulias Davis"
                }
            ],
            "description": "A library for validating emails against several RFCs",
            "homepage": "https://github.com/egulias/EmailValidator",
            "keywords": [
                "email",
                "emailvalidation",
                "emailvalidator",
                "validation",
                "validator"
            ],
            "support": {
                "issues": "https://github.com/egulias/EmailValidator/issues",
                "source": "https://github.com/egulias/EmailValidator/tree/4.0.3"
            },
            "funding": [
                {
                    "url": "https://github.com/egulias",
                    "type": "github"
                }
            ],
            "time": "2024-12-27T00:36:43+00:00"
        },
        {
            "name": "erusev/parsedown",
            "version": "1.7.4",
            "source": {
                "type": "git",
                "url": "https://github.com/erusev/parsedown.git",
                "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3",
                "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3",
                "shasum": ""
            },
            "require": {
                "ext-mbstring": "*",
                "php": ">=5.3.0"
            },
            "require-dev": {
                "phpunit/phpunit": "^4.8.35"
            },
            "type": "library",
            "autoload": {
                "psr-0": {
                    "Parsedown": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Emanuil Rusev",
                    "email": "<EMAIL>",
                    "homepage": "http://erusev.com"
                }
            ],
            "description": "Parser for Markdown.",
            "homepage": "http://parsedown.org",
            "keywords": [
                "markdown",
                "parser"
            ],
            "support": {
                "issues": "https://github.com/erusev/parsedown/issues",
                "source": "https://github.com/erusev/parsedown/tree/1.7.x"
            },
            "time": "2019-12-30T22:54:17+00:00"
        },
        {
            "name": "ezyang/htmlpurifier",
            "version": "v4.18.0",
            "source": {
                "type": "git",
                "url": "https://github.com/ezyang/htmlpurifier.git",
                "reference": "cb56001e54359df7ae76dc522d08845dc741621b"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b",
                "reference": "cb56001e54359df7ae76dc522d08845dc741621b",
                "shasum": ""
            },
            "require": {
                "php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"
            },
            "require-dev": {
                "cerdic/css-tidy": "^1.7 || ^2.0",
                "simpletest/simpletest": "dev-master"
            },
            "suggest": {
                "cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.",
                "ext-bcmath": "Used for unit conversion and imagecrash protection",
                "ext-iconv": "Converts text to and from non-UTF-8 encodings",
                "ext-tidy": "Used for pretty-printing HTML"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "library/HTMLPurifier.composer.php"
                ],
                "psr-0": {
                    "HTMLPurifier": "library/"
                },
                "exclude-from-classmap": [
                    "/library/HTMLPurifier/Language/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "LGPL-2.1-or-later"
            ],
            "authors": [
                {
                    "name": "Edward Z. Yang",
                    "email": "<EMAIL>",
                    "homepage": "http://ezyang.com"
                }
            ],
            "description": "Standards compliant HTML filter written in PHP",
            "homepage": "http://htmlpurifier.org/",
            "keywords": [
                "html"
            ],
            "support": {
                "issues": "https://github.com/ezyang/htmlpurifier/issues",
                "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"
            },
            "time": "2024-11-01T03:51:45+00:00"
        },
        {
            "name": "fakerphp/faker",
            "version": "v1.24.1",
            "source": {
                "type": "git",
                "url": "https://github.com/FakerPHP/Faker.git",
                "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5",
                "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5",
                "shasum": ""
            },
            "require": {
                "php": "^7.4 || ^8.0",
                "psr/container": "^1.0 || ^2.0",
                "symfony/deprecation-contracts": "^2.2 || ^3.0"
            },
            "conflict": {
                "fzaninotto/faker": "*"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.4.1",
                "doctrine/persistence": "^1.3 || ^2.0",
                "ext-intl": "*",
                "phpunit/phpunit": "^9.5.26",
                "symfony/phpunit-bridge": "^5.4.16"
            },
            "suggest": {
                "doctrine/orm": "Required to use Faker\\ORM\\Doctrine",
                "ext-curl": "Required by Faker\\Provider\\Image to download images.",
                "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.",
                "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.",
                "ext-mbstring": "Required for multibyte Unicode string functionality."
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Faker\\": "src/Faker/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "François Zaninotto"
                }
            ],
            "description": "Faker is a PHP library that generates fake data for you.",
            "keywords": [
                "data",
                "faker",
                "fixtures"
            ],
            "support": {
                "issues": "https://github.com/FakerPHP/Faker/issues",
                "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"
            },
            "time": "2024-11-21T13:46:39+00:00"
        },
        {
            "name": "filp/whoops",
            "version": "2.17.0",
            "source": {
                "type": "git",
                "url": "https://github.com/filp/whoops.git",
                "reference": "075bc0c26631110584175de6523ab3f1652eb28e"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/filp/whoops/zipball/075bc0c26631110584175de6523ab3f1652eb28e",
                "reference": "075bc0c26631110584175de6523ab3f1652eb28e",
                "shasum": ""
            },
            "require": {
                "php": "^7.1 || ^8.0",
                "psr/log": "^1.0.1 || ^2.0 || ^3.0"
            },
            "require-dev": {
                "mockery/mockery": "^1.0",
                "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.3.3",
                "symfony/var-dumper": "^4.0 || ^5.0"
            },
            "suggest": {
                "symfony/var-dumper": "Pretty print complex values better with var-dumper available",
                "whoops/soap": "Formats errors as SOAP responses"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.7-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Whoops\\": "src/Whoops/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Filipe Dobreira",
                    "homepage": "https://github.com/filp",
                    "role": "Developer"
                }
            ],
            "description": "php error handling for cool kids",
            "homepage": "https://filp.github.io/whoops/",
            "keywords": [
                "error",
                "exception",
                "handling",
                "library",
                "throwable",
                "whoops"
            ],
            "support": {
                "issues": "https://github.com/filp/whoops/issues",
                "source": "https://github.com/filp/whoops/tree/2.17.0"
            },
            "funding": [
                {
                    "url": "https://github.com/denis-sokolov",
                    "type": "github"
                }
            ],
            "time": "2025-01-25T12:00:00+00:00"
        },
        {
            "name": "firebase/php-jwt",
            "version": "v6.11.0",
            "source": {
                "type": "git",
                "url": "https://github.com/firebase/php-jwt.git",
                "reference": "8f718f4dfc9c5d5f0c994cdfd103921b43592712"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/firebase/php-jwt/zipball/8f718f4dfc9c5d5f0c994cdfd103921b43592712",
                "reference": "8f718f4dfc9c5d5f0c994cdfd103921b43592712",
                "shasum": ""
            },
            "require": {
                "php": "^8.0"
            },
            "require-dev": {
                "guzzlehttp/guzzle": "^7.4",
                "phpspec/prophecy-phpunit": "^2.0",
                "phpunit/phpunit": "^9.5",
                "psr/cache": "^2.0||^3.0",
                "psr/http-client": "^1.0",
                "psr/http-factory": "^1.0"
            },
            "suggest": {
                "ext-sodium": "Support EdDSA (Ed25519) signatures",
                "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Firebase\\JWT\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Neuman Vong",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Anant Narayanan",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.",
            "homepage": "https://github.com/firebase/php-jwt",
            "keywords": [
                "jwt",
                "php"
            ],
            "support": {
                "issues": "https://github.com/firebase/php-jwt/issues",
                "source": "https://github.com/firebase/php-jwt/tree/v6.11.0"
            },
            "time": "2025-01-23T05:11:06+00:00"
        },
        {
            "name": "fruitcake/php-cors",
            "version": "v1.3.0",
            "source": {
                "type": "git",
                "url": "https://github.com/fruitcake/php-cors.git",
                "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b",
                "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b",
                "shasum": ""
            },
            "require": {
                "php": "^7.4|^8.0",
                "symfony/http-foundation": "^4.4|^5.4|^6|^7"
            },
            "require-dev": {
                "phpstan/phpstan": "^1.4",
                "phpunit/phpunit": "^9",
                "squizlabs/php_codesniffer": "^3.5"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.2-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Fruitcake\\Cors\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fruitcake",
                    "homepage": "https://fruitcake.nl"
                },
                {
                    "name": "Barryvdh",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Cross-origin resource sharing library for the Symfony HttpFoundation",
            "homepage": "https://github.com/fruitcake/php-cors",
            "keywords": [
                "cors",
                "laravel",
                "symfony"
            ],
            "support": {
                "issues": "https://github.com/fruitcake/php-cors/issues",
                "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"
            },
            "funding": [
                {
                    "url": "https://fruitcake.nl",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/barryvdh",
                    "type": "github"
                }
            ],
            "time": "2023-10-12T05:21:21+00:00"
        },
        {
            "name": "graham-campbell/result-type",
            "version": "v1.1.3",
            "source": {
                "type": "git",
                "url": "https://github.com/GrahamCampbell/Result-Type.git",
                "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945",
                "reference": "3ba905c11371512af9d9bdd27d99b782216b6945",
                "shasum": ""
            },
            "require": {
                "php": "^7.2.5 || ^8.0",
                "phpoption/phpoption": "^1.9.3"
            },
            "require-dev": {
                "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "GrahamCampbell\\ResultType\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                }
            ],
            "description": "An Implementation Of The Result Type",
            "keywords": [
                "Graham Campbell",
                "GrahamCampbell",
                "Result Type",
                "Result-Type",
                "result"
            ],
            "support": {
                "issues": "https://github.com/GrahamCampbell/Result-Type/issues",
                "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type",
                    "type": "tidelift"
                }
            ],
            "time": "2024-07-20T21:45:45+00:00"
        },
        {
            "name": "guzzlehttp/guzzle",
            "version": "7.9.2",
            "source": {
                "type": "git",
                "url": "https://github.com/guzzle/guzzle.git",
                "reference": "d281ed313b989f213357e3be1a179f02196ac99b"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b",
                "reference": "d281ed313b989f213357e3be1a179f02196ac99b",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "guzzlehttp/promises": "^1.5.3 || ^2.0.3",
                "guzzlehttp/psr7": "^2.7.0",
                "php": "^7.2.5 || ^8.0",
                "psr/http-client": "^1.0",
                "symfony/deprecation-contracts": "^2.2 || ^3.0"
            },
            "provide": {
                "psr/http-client-implementation": "1.0"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.8.2",
                "ext-curl": "*",
                "guzzle/client-integration-tests": "3.0.2",
                "php-http/message-factory": "^1.1",
                "phpunit/phpunit": "^8.5.39 || ^9.6.20",
                "psr/log": "^1.1 || ^2.0 || ^3.0"
            },
            "suggest": {
                "ext-curl": "Required for CURL handler support",
                "ext-intl": "Required for Internationalized Domain Name (IDN) support",
                "psr/log": "Required for using the Log middleware"
            },
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": true,
                    "forward-command": false
                }
            },
            "autoload": {
                "files": [
                    "src/functions_include.php"
                ],
                "psr-4": {
                    "GuzzleHttp\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                },
                {
                    "name": "Michael Dowling",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/mtdowling"
                },
                {
                    "name": "Jeremy Lindblom",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/jeremeamia"
                },
                {
                    "name": "George Mponos",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/gmponos"
                },
                {
                    "name": "Tobias Nyholm",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Nyholm"
                },
                {
                    "name": "Márk Sági-Kazár",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/sagikazarmark"
                },
                {
                    "name": "Tobias Schultze",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Tobion"
                }
            ],
            "description": "Guzzle is a PHP HTTP client library",
            "keywords": [
                "client",
                "curl",
                "framework",
                "http",
                "http client",
                "psr-18",
                "psr-7",
                "rest",
                "web service"
            ],
            "support": {
                "issues": "https://github.com/guzzle/guzzle/issues",
                "source": "https://github.com/guzzle/guzzle/tree/7.9.2"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://github.com/Nyholm",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle",
                    "type": "tidelift"
                }
            ],
            "time": "2024-07-24T11:22:20+00:00"
        },
        {
            "name": "guzzlehttp/promises",
            "version": "2.0.4",
            "source": {
                "type": "git",
                "url": "https://github.com/guzzle/promises.git",
                "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455",
                "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455",
                "shasum": ""
            },
            "require": {
                "php": "^7.2.5 || ^8.0"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.8.2",
                "phpunit/phpunit": "^8.5.39 || ^9.6.20"
            },
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": true,
                    "forward-command": false
                }
            },
            "autoload": {
                "psr-4": {
                    "GuzzleHttp\\Promise\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                },
                {
                    "name": "Michael Dowling",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/mtdowling"
                },
                {
                    "name": "Tobias Nyholm",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Nyholm"
                },
                {
                    "name": "Tobias Schultze",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Tobion"
                }
            ],
            "description": "Guzzle promises library",
            "keywords": [
                "promise"
            ],
            "support": {
                "issues": "https://github.com/guzzle/promises/issues",
                "source": "https://github.com/guzzle/promises/tree/2.0.4"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://github.com/Nyholm",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises",
                    "type": "tidelift"
                }
            ],
            "time": "2024-10-17T10:06:22+00:00"
        },
        {
            "name": "guzzlehttp/psr7",
            "version": "2.7.0",
            "source": {
                "type": "git",
                "url": "https://github.com/guzzle/psr7.git",
                "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201",
                "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201",
                "shasum": ""
            },
            "require": {
                "php": "^7.2.5 || ^8.0",
                "psr/http-factory": "^1.0",
                "psr/http-message": "^1.1 || ^2.0",
                "ralouphie/getallheaders": "^3.0"
            },
            "provide": {
                "psr/http-factory-implementation": "1.0",
                "psr/http-message-implementation": "1.0"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.8.2",
                "http-interop/http-factory-tests": "0.9.0",
                "phpunit/phpunit": "^8.5.39 || ^9.6.20"
            },
            "suggest": {
                "laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"
            },
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": true,
                    "forward-command": false
                }
            },
            "autoload": {
                "psr-4": {
                    "GuzzleHttp\\Psr7\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                },
                {
                    "name": "Michael Dowling",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/mtdowling"
                },
                {
                    "name": "George Mponos",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/gmponos"
                },
                {
                    "name": "Tobias Nyholm",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Nyholm"
                },
                {
                    "name": "Márk Sági-Kazár",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/sagikazarmark"
                },
                {
                    "name": "Tobias Schultze",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Tobion"
                },
                {
                    "name": "Márk Sági-Kazár",
                    "email": "<EMAIL>",
                    "homepage": "https://sagikazarmark.hu"
                }
            ],
            "description": "PSR-7 message implementation that also provides common utility methods",
            "keywords": [
                "http",
                "message",
                "psr-7",
                "request",
                "response",
                "stream",
                "uri",
                "url"
            ],
            "support": {
                "issues": "https://github.com/guzzle/psr7/issues",
                "source": "https://github.com/guzzle/psr7/tree/2.7.0"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://github.com/Nyholm",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7",
                    "type": "tidelift"
                }
            ],
            "time": "2024-07-18T11:15:46+00:00"
        },
        {
            "name": "guzzlehttp/uri-template",
            "version": "v1.0.4",
            "source": {
                "type": "git",
                "url": "https://github.com/guzzle/uri-template.git",
                "reference": "30e286560c137526eccd4ce21b2de477ab0676d2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/guzzle/uri-template/zipball/30e286560c137526eccd4ce21b2de477ab0676d2",
                "reference": "30e286560c137526eccd4ce21b2de477ab0676d2",
                "shasum": ""
            },
            "require": {
                "php": "^7.2.5 || ^8.0",
                "symfony/polyfill-php80": "^1.24"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.8.2",
                "phpunit/phpunit": "^8.5.36 || ^9.6.15",
                "uri-template/tests": "1.0.0"
            },
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": true,
                    "forward-command": false
                }
            },
            "autoload": {
                "psr-4": {
                    "GuzzleHttp\\UriTemplate\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                },
                {
                    "name": "Michael Dowling",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/mtdowling"
                },
                {
                    "name": "George Mponos",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/gmponos"
                },
                {
                    "name": "Tobias Nyholm",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/Nyholm"
                }
            ],
            "description": "A polyfill class for uri_template of PHP",
            "keywords": [
                "guzzlehttp",
                "uri-template"
            ],
            "support": {
                "issues": "https://github.com/guzzle/uri-template/issues",
                "source": "https://github.com/guzzle/uri-template/tree/v1.0.4"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://github.com/Nyholm",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-03T10:55:03+00:00"
        },
        {
            "name": "knuckleswtf/scribe",
            "version": "5.1.0",
            "source": {
                "type": "git",
                "url": "https://github.com/knuckleswtf/scribe.git",
                "reference": "1e846244476a0312b8a7731a779a4fea85a5bfa0"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/knuckleswtf/scribe/zipball/1e846244476a0312b8a7731a779a4fea85a5bfa0",
                "reference": "1e846244476a0312b8a7731a779a4fea85a5bfa0",
                "shasum": ""
            },
            "require": {
                "erusev/parsedown": "1.7.4",
                "ext-fileinfo": "*",
                "ext-json": "*",
                "ext-pdo": "*",
                "fakerphp/faker": "^1.23.1",
                "laravel/framework": "^9.0|^10.0|^11.0|^12.0",
                "league/flysystem": "^3.0",
                "mpociot/reflection-docblock": "^1.0.1",
                "nikic/php-parser": "^5.0",
                "nunomaduro/collision": "^6.0|^7.0|^8.0",
                "php": ">=8.1",
                "ramsey/uuid": "^4.2.2",
                "shalvah/clara": "^3.1.0",
                "shalvah/upgrader": ">=0.6.0",
                "spatie/data-transfer-object": "^2.6|^3.0",
                "symfony/var-exporter": "^6.0|^7.0",
                "symfony/yaml": "^6.0|^7.0"
            },
            "replace": {
                "mpociot/laravel-apidoc-generator": "*"
            },
            "require-dev": {
                "dms/phpunit-arraysubset-asserts": "^v0.5.0",
                "laravel/legacy-factories": "^1.3.0",
                "league/fractal": "^0.20",
                "nikic/fast-route": "^1.3",
                "orchestra/testbench": "^7.0|^8.0|^v9.10.0|^10.0",
                "pestphp/pest": "^1.21|^2.0|^3.0",
                "phpstan/phpstan": "^2.1.5",
                "phpunit/phpunit": "^9.0|^10.0|^11.0",
                "spatie/ray": "^1.41",
                "symfony/css-selector": "^6.0|^7.0",
                "symfony/dom-crawler": "^6.0|^7.0"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Knuckles\\Scribe\\ScribeServiceProvider"
                    ]
                }
            },
            "autoload": {
                "files": [
                    "src/Config/helpers.php"
                ],
                "psr-4": {
                    "Knuckles\\Camel\\": "camel/",
                    "Knuckles\\Scribe\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Shalvah"
                }
            ],
            "description": "Generate API documentation for humans from your Laravel codebase.✍",
            "homepage": "http://github.com/knuckleswtf/scribe",
            "keywords": [
                "api",
                "documentation",
                "laravel"
            ],
            "support": {
                "issues": "https://github.com/knuckleswtf/scribe/issues",
                "source": "https://github.com/knuckleswtf/scribe/tree/5.1.0"
            },
            "funding": [
                {
                    "url": "https://patreon.com/shalvah",
                    "type": "patreon"
                }
            ],
            "time": "2025-02-25T09:09:08+00:00"
        },
        {
            "name": "laravel/framework",
            "version": "v11.44.1",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/framework.git",
                "reference": "0883d4175f4e2b5c299e7087ad3c74f2ce195c6d"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/framework/zipball/0883d4175f4e2b5c299e7087ad3c74f2ce195c6d",
                "reference": "0883d4175f4e2b5c299e7087ad3c74f2ce195c6d",
                "shasum": ""
            },
            "require": {
                "brick/math": "^0.9.3|^0.10.2|^0.11|^0.12",
                "composer-runtime-api": "^2.2",
                "doctrine/inflector": "^2.0.5",
                "dragonmantank/cron-expression": "^3.4",
                "egulias/email-validator": "^3.2.1|^4.0",
                "ext-ctype": "*",
                "ext-filter": "*",
                "ext-hash": "*",
                "ext-mbstring": "*",
                "ext-openssl": "*",
                "ext-session": "*",
                "ext-tokenizer": "*",
                "fruitcake/php-cors": "^1.3",
                "guzzlehttp/guzzle": "^7.8.2",
                "guzzlehttp/uri-template": "^1.0",
                "laravel/prompts": "^0.1.18|^0.2.0|^0.3.0",
                "laravel/serializable-closure": "^1.3|^2.0",
                "league/commonmark": "^2.6",
                "league/flysystem": "^3.25.1",
                "league/flysystem-local": "^3.25.1",
                "league/uri": "^7.5.1",
                "monolog/monolog": "^3.0",
                "nesbot/carbon": "^2.72.6|^3.8.4",
                "nunomaduro/termwind": "^2.0",
                "php": "^8.2",
                "psr/container": "^1.1.1|^2.0.1",
                "psr/log": "^1.0|^2.0|^3.0",
                "psr/simple-cache": "^1.0|^2.0|^3.0",
                "ramsey/uuid": "^4.7",
                "symfony/console": "^7.0.3",
                "symfony/error-handler": "^7.0.3",
                "symfony/finder": "^7.0.3",
                "symfony/http-foundation": "^7.2.0",
                "symfony/http-kernel": "^7.0.3",
                "symfony/mailer": "^7.0.3",
                "symfony/mime": "^7.0.3",
                "symfony/polyfill-php83": "^1.31",
                "symfony/process": "^7.0.3",
                "symfony/routing": "^7.0.3",
                "symfony/uid": "^7.0.3",
                "symfony/var-dumper": "^7.0.3",
                "tijsverkoyen/css-to-inline-styles": "^2.2.5",
                "vlucas/phpdotenv": "^5.6.1",
                "voku/portable-ascii": "^2.0.2"
            },
            "conflict": {
                "tightenco/collect": "<5.5.33"
            },
            "provide": {
                "psr/container-implementation": "1.1|2.0",
                "psr/log-implementation": "1.0|2.0|3.0",
                "psr/simple-cache-implementation": "1.0|2.0|3.0"
            },
            "replace": {
                "illuminate/auth": "self.version",
                "illuminate/broadcasting": "self.version",
                "illuminate/bus": "self.version",
                "illuminate/cache": "self.version",
                "illuminate/collections": "self.version",
                "illuminate/concurrency": "self.version",
                "illuminate/conditionable": "self.version",
                "illuminate/config": "self.version",
                "illuminate/console": "self.version",
                "illuminate/container": "self.version",
                "illuminate/contracts": "self.version",
                "illuminate/cookie": "self.version",
                "illuminate/database": "self.version",
                "illuminate/encryption": "self.version",
                "illuminate/events": "self.version",
                "illuminate/filesystem": "self.version",
                "illuminate/hashing": "self.version",
                "illuminate/http": "self.version",
                "illuminate/log": "self.version",
                "illuminate/macroable": "self.version",
                "illuminate/mail": "self.version",
                "illuminate/notifications": "self.version",
                "illuminate/pagination": "self.version",
                "illuminate/pipeline": "self.version",
                "illuminate/process": "self.version",
                "illuminate/queue": "self.version",
                "illuminate/redis": "self.version",
                "illuminate/routing": "self.version",
                "illuminate/session": "self.version",
                "illuminate/support": "self.version",
                "illuminate/testing": "self.version",
                "illuminate/translation": "self.version",
                "illuminate/validation": "self.version",
                "illuminate/view": "self.version",
                "spatie/once": "*"
            },
            "require-dev": {
                "ably/ably-php": "^1.0",
                "aws/aws-sdk-php": "^3.322.9",
                "ext-gmp": "*",
                "fakerphp/faker": "^1.24",
                "guzzlehttp/promises": "^2.0.3",
                "guzzlehttp/psr7": "^2.4",
                "laravel/pint": "^1.18",
                "league/flysystem-aws-s3-v3": "^3.25.1",
                "league/flysystem-ftp": "^3.25.1",
                "league/flysystem-path-prefixing": "^3.25.1",
                "league/flysystem-read-only": "^3.25.1",
                "league/flysystem-sftp-v3": "^3.25.1",
                "mockery/mockery": "^1.6.10",
                "orchestra/testbench-core": "^9.11.2",
                "pda/pheanstalk": "^5.0.6",
                "php-http/discovery": "^1.15",
                "phpstan/phpstan": "^2.0",
                "phpunit/phpunit": "^10.5.35|^11.3.6|^12.0.1",
                "predis/predis": "^2.3",
                "resend/resend-php": "^0.10.0",
                "symfony/cache": "^7.0.3",
                "symfony/http-client": "^7.0.3",
                "symfony/psr-http-message-bridge": "^7.0.3",
                "symfony/translation": "^7.0.3"
            },
            "suggest": {
                "ably/ably-php": "Required to use the Ably broadcast driver (^1.0).",
                "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage, and SES mail driver (^3.322.9).",
                "brianium/paratest": "Required to run tests in parallel (^7.0|^8.0).",
                "ext-apcu": "Required to use the APC cache driver.",
                "ext-fileinfo": "Required to use the Filesystem class.",
                "ext-ftp": "Required to use the Flysystem FTP driver.",
                "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().",
                "ext-memcached": "Required to use the memcache cache driver.",
                "ext-pcntl": "Required to use all features of the queue worker and console signal trapping.",
                "ext-pdo": "Required to use all database features.",
                "ext-posix": "Required to use all features of the queue worker.",
                "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0|^6.0).",
                "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).",
                "filp/whoops": "Required for friendly error pages in development (^2.14.3).",
                "laravel/tinker": "Required to use the tinker console command (^2.0).",
                "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.25.1).",
                "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.25.1).",
                "league/flysystem-path-prefixing": "Required to use the scoped driver (^3.25.1).",
                "league/flysystem-read-only": "Required to use read-only disks (^3.25.1)",
                "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.25.1).",
                "mockery/mockery": "Required to use mocking (^1.6).",
                "pda/pheanstalk": "Required to use the beanstalk queue driver (^5.0).",
                "php-http/discovery": "Required to use PSR-7 bridging features (^1.15).",
                "phpunit/phpunit": "Required to use assertions and run tests (^10.5.35|^11.3.6|^12.0.1).",
                "predis/predis": "Required to use the predis connector (^2.3).",
                "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).",
                "pusher/pusher-php-server": "Required to use the Pusher broadcast driver (^6.0|^7.0).",
                "resend/resend-php": "Required to enable support for the Resend mail transport (^0.10.0).",
                "symfony/cache": "Required to PSR-6 cache bridge (^7.0).",
                "symfony/filesystem": "Required to enable support for relative symbolic links (^7.0).",
                "symfony/http-client": "Required to enable support for the Symfony API mail transports (^7.0).",
                "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^7.0).",
                "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^7.0).",
                "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^7.0)."
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "11.x-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/Illuminate/Collections/functions.php",
                    "src/Illuminate/Collections/helpers.php",
                    "src/Illuminate/Events/functions.php",
                    "src/Illuminate/Filesystem/functions.php",
                    "src/Illuminate/Foundation/helpers.php",
                    "src/Illuminate/Log/functions.php",
                    "src/Illuminate/Support/functions.php",
                    "src/Illuminate/Support/helpers.php"
                ],
                "psr-4": {
                    "Illuminate\\": "src/Illuminate/",
                    "Illuminate\\Support\\": [
                        "src/Illuminate/Macroable/",
                        "src/Illuminate/Collections/",
                        "src/Illuminate/Conditionable/"
                    ]
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                }
            ],
            "description": "The Laravel Framework.",
            "homepage": "https://laravel.com",
            "keywords": [
                "framework",
                "laravel"
            ],
            "support": {
                "issues": "https://github.com/laravel/framework/issues",
                "source": "https://github.com/laravel/framework"
            },
            "time": "2025-03-05T15:34:10+00:00"
        },
        {
            "name": "laravel/prompts",
            "version": "v0.3.5",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/prompts.git",
                "reference": "57b8f7efe40333cdb925700891c7d7465325d3b1"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/prompts/zipball/57b8f7efe40333cdb925700891c7d7465325d3b1",
                "reference": "57b8f7efe40333cdb925700891c7d7465325d3b1",
                "shasum": ""
            },
            "require": {
                "composer-runtime-api": "^2.2",
                "ext-mbstring": "*",
                "php": "^8.1",
                "symfony/console": "^6.2|^7.0"
            },
            "conflict": {
                "illuminate/console": ">=10.17.0 <10.25.0",
                "laravel/framework": ">=10.17.0 <10.25.0"
            },
            "require-dev": {
                "illuminate/collections": "^10.0|^11.0|^12.0",
                "mockery/mockery": "^1.5",
                "pestphp/pest": "^2.3|^3.4",
                "phpstan/phpstan": "^1.11",
                "phpstan/phpstan-mockery": "^1.1"
            },
            "suggest": {
                "ext-pcntl": "Required for the spinner to be animated."
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "0.3.x-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/helpers.php"
                ],
                "psr-4": {
                    "Laravel\\Prompts\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "description": "Add beautiful and user-friendly forms to your command-line applications.",
            "support": {
                "issues": "https://github.com/laravel/prompts/issues",
                "source": "https://github.com/laravel/prompts/tree/v0.3.5"
            },
            "time": "2025-02-11T13:34:40+00:00"
        },
        {
            "name": "laravel/sanctum",
            "version": "v4.0.8",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/sanctum.git",
                "reference": "ec1dd9ddb2ab370f79dfe724a101856e0963f43c"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/sanctum/zipball/ec1dd9ddb2ab370f79dfe724a101856e0963f43c",
                "reference": "ec1dd9ddb2ab370f79dfe724a101856e0963f43c",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "illuminate/console": "^11.0|^12.0",
                "illuminate/contracts": "^11.0|^12.0",
                "illuminate/database": "^11.0|^12.0",
                "illuminate/support": "^11.0|^12.0",
                "php": "^8.2",
                "symfony/console": "^7.0"
            },
            "require-dev": {
                "mockery/mockery": "^1.6",
                "orchestra/testbench": "^9.0|^10.0",
                "phpstan/phpstan": "^1.10",
                "phpunit/phpunit": "^11.3"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Laravel\\Sanctum\\SanctumServiceProvider"
                    ]
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\Sanctum\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.",
            "keywords": [
                "auth",
                "laravel",
                "sanctum"
            ],
            "support": {
                "issues": "https://github.com/laravel/sanctum/issues",
                "source": "https://github.com/laravel/sanctum"
            },
            "time": "2025-01-26T19:34:36+00:00"
        },
        {
            "name": "laravel/scout",
            "version": "v10.13.1",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/scout.git",
                "reference": "577535cd93474e4c915e7469cbfa597c41aef8e2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/scout/zipball/577535cd93474e4c915e7469cbfa597c41aef8e2",
                "reference": "577535cd93474e4c915e7469cbfa597c41aef8e2",
                "shasum": ""
            },
            "require": {
                "illuminate/bus": "^9.0|^10.0|^11.0|^12.0",
                "illuminate/contracts": "^9.0|^10.0|^11.0|^12.0",
                "illuminate/database": "^9.0|^10.0|^11.0|^12.0",
                "illuminate/http": "^9.0|^10.0|^11.0|^12.0",
                "illuminate/pagination": "^9.0|^10.0|^11.0|^12.0",
                "illuminate/queue": "^9.0|^10.0|^11.0|^12.0",
                "illuminate/support": "^9.0|^10.0|^11.0|^12.0",
                "php": "^8.0",
                "symfony/console": "^6.0|^7.0"
            },
            "conflict": {
                "algolia/algoliasearch-client-php": "<3.2.0|>=5.0.0"
            },
            "require-dev": {
                "algolia/algoliasearch-client-php": "^3.2|^4.0",
                "meilisearch/meilisearch-php": "^1.0",
                "mockery/mockery": "^1.0",
                "orchestra/testbench": "^7.31|^8.11|^9.0|^10.0",
                "php-http/guzzle7-adapter": "^1.0",
                "phpstan/phpstan": "^1.10",
                "phpunit/phpunit": "^9.3|^10.4",
                "typesense/typesense-php": "^4.9.3"
            },
            "suggest": {
                "algolia/algoliasearch-client-php": "Required to use the Algolia engine (^3.2).",
                "meilisearch/meilisearch-php": "Required to use the Meilisearch engine (^1.0).",
                "typesense/typesense-php": "Required to use the Typesense engine (^4.9)."
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Laravel\\Scout\\ScoutServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-master": "10.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\Scout\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Laravel Scout provides a driver based solution to searching your Eloquent models.",
            "keywords": [
                "algolia",
                "laravel",
                "search"
            ],
            "support": {
                "issues": "https://github.com/laravel/scout/issues",
                "source": "https://github.com/laravel/scout"
            },
            "time": "2025-02-18T18:39:33+00:00"
        },
        {
            "name": "laravel/serializable-closure",
            "version": "v2.0.3",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/serializable-closure.git",
                "reference": "f379c13663245f7aa4512a7869f62eb14095f23f"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/f379c13663245f7aa4512a7869f62eb14095f23f",
                "reference": "f379c13663245f7aa4512a7869f62eb14095f23f",
                "shasum": ""
            },
            "require": {
                "php": "^8.1"
            },
            "require-dev": {
                "illuminate/support": "^10.0|^11.0|^12.0",
                "nesbot/carbon": "^2.67|^3.0",
                "pestphp/pest": "^2.36|^3.0",
                "phpstan/phpstan": "^2.0",
                "symfony/var-dumper": "^6.2.0|^7.0.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\SerializableClosure\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Nuno Maduro",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.",
            "keywords": [
                "closure",
                "laravel",
                "serializable"
            ],
            "support": {
                "issues": "https://github.com/laravel/serializable-closure/issues",
                "source": "https://github.com/laravel/serializable-closure"
            },
            "time": "2025-02-11T15:03:05+00:00"
        },
        {
            "name": "laravel/socialite",
            "version": "v5.18.0",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/socialite.git",
                "reference": "7809dc71250e074cd42970f0f803f2cddc04c5de"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/socialite/zipball/7809dc71250e074cd42970f0f803f2cddc04c5de",
                "reference": "7809dc71250e074cd42970f0f803f2cddc04c5de",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "firebase/php-jwt": "^6.4",
                "guzzlehttp/guzzle": "^6.0|^7.0",
                "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0",
                "illuminate/http": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0",
                "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0",
                "league/oauth1-client": "^1.11",
                "php": "^7.2|^8.0",
                "phpseclib/phpseclib": "^3.0"
            },
            "require-dev": {
                "mockery/mockery": "^1.0",
                "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0|^9.0|^10.0",
                "phpstan/phpstan": "^1.10",
                "phpunit/phpunit": "^8.0|^9.3|^10.4|^11.5"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "aliases": {
                        "Socialite": "Laravel\\Socialite\\Facades\\Socialite"
                    },
                    "providers": [
                        "Laravel\\Socialite\\SocialiteServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-master": "5.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\Socialite\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Laravel wrapper around OAuth 1 & OAuth 2 libraries.",
            "homepage": "https://laravel.com",
            "keywords": [
                "laravel",
                "oauth"
            ],
            "support": {
                "issues": "https://github.com/laravel/socialite/issues",
                "source": "https://github.com/laravel/socialite"
            },
            "time": "2025-02-11T13:38:19+00:00"
        },
        {
            "name": "laravel/tinker",
            "version": "v2.10.1",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/tinker.git",
                "reference": "22177cc71807d38f2810c6204d8f7183d88a57d3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/tinker/zipball/22177cc71807d38f2810c6204d8f7183d88a57d3",
                "reference": "22177cc71807d38f2810c6204d8f7183d88a57d3",
                "shasum": ""
            },
            "require": {
                "illuminate/console": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0",
                "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0",
                "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0",
                "php": "^7.2.5|^8.0",
                "psy/psysh": "^0.11.1|^0.12.0",
                "symfony/var-dumper": "^4.3.4|^5.0|^6.0|^7.0"
            },
            "require-dev": {
                "mockery/mockery": "~1.3.3|^1.4.2",
                "phpstan/phpstan": "^1.10",
                "phpunit/phpunit": "^8.5.8|^9.3.3|^10.0"
            },
            "suggest": {
                "illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0)."
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Laravel\\Tinker\\TinkerServiceProvider"
                    ]
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\Tinker\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Powerful REPL for the Laravel framework.",
            "keywords": [
                "REPL",
                "Tinker",
                "laravel",
                "psysh"
            ],
            "support": {
                "issues": "https://github.com/laravel/tinker/issues",
                "source": "https://github.com/laravel/tinker/tree/v2.10.1"
            },
            "time": "2025-01-27T14:24:01+00:00"
        },
        {
            "name": "lcobucci/clock",
            "version": "3.3.1",
            "source": {
                "type": "git",
                "url": "https://github.com/lcobucci/clock.git",
                "reference": "db3713a61addfffd615b79bf0bc22f0ccc61b86b"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/lcobucci/clock/zipball/db3713a61addfffd615b79bf0bc22f0ccc61b86b",
                "reference": "db3713a61addfffd615b79bf0bc22f0ccc61b86b",
                "shasum": ""
            },
            "require": {
                "php": "~8.2.0 || ~8.3.0 || ~8.4.0",
                "psr/clock": "^1.0"
            },
            "provide": {
                "psr/clock-implementation": "1.0"
            },
            "require-dev": {
                "infection/infection": "^0.29",
                "lcobucci/coding-standard": "^11.1.0",
                "phpstan/extension-installer": "^1.3.1",
                "phpstan/phpstan": "^1.10.25",
                "phpstan/phpstan-deprecation-rules": "^1.1.3",
                "phpstan/phpstan-phpunit": "^1.3.13",
                "phpstan/phpstan-strict-rules": "^1.5.1",
                "phpunit/phpunit": "^11.3.6"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Lcobucci\\Clock\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Luís Cobucci",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Yet another clock abstraction",
            "support": {
                "issues": "https://github.com/lcobucci/clock/issues",
                "source": "https://github.com/lcobucci/clock/tree/3.3.1"
            },
            "funding": [
                {
                    "url": "https://github.com/lcobucci",
                    "type": "github"
                },
                {
                    "url": "https://www.patreon.com/lcobucci",
                    "type": "patreon"
                }
            ],
            "time": "2024-09-24T20:45:14+00:00"
        },
        {
            "name": "lcobucci/jwt",
            "version": "5.5.0",
            "source": {
                "type": "git",
                "url": "https://github.com/lcobucci/jwt.git",
                "reference": "a835af59b030d3f2967725697cf88300f579088e"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/lcobucci/jwt/zipball/a835af59b030d3f2967725697cf88300f579088e",
                "reference": "a835af59b030d3f2967725697cf88300f579088e",
                "shasum": ""
            },
            "require": {
                "ext-openssl": "*",
                "ext-sodium": "*",
                "php": "~8.2.0 || ~8.3.0 || ~8.4.0",
                "psr/clock": "^1.0"
            },
            "require-dev": {
                "infection/infection": "^0.29",
                "lcobucci/clock": "^3.2",
                "lcobucci/coding-standard": "^11.0",
                "phpbench/phpbench": "^1.2",
                "phpstan/extension-installer": "^1.2",
                "phpstan/phpstan": "^1.10.7",
                "phpstan/phpstan-deprecation-rules": "^1.1.3",
                "phpstan/phpstan-phpunit": "^1.3.10",
                "phpstan/phpstan-strict-rules": "^1.5.0",
                "phpunit/phpunit": "^11.1"
            },
            "suggest": {
                "lcobucci/clock": ">= 3.2"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Lcobucci\\JWT\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Luís Cobucci",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "A simple library to work with JSON Web Token and JSON Web Signature",
            "keywords": [
                "JWS",
                "jwt"
            ],
            "support": {
                "issues": "https://github.com/lcobucci/jwt/issues",
                "source": "https://github.com/lcobucci/jwt/tree/5.5.0"
            },
            "funding": [
                {
                    "url": "https://github.com/lcobucci",
                    "type": "github"
                },
                {
                    "url": "https://www.patreon.com/lcobucci",
                    "type": "patreon"
                }
            ],
            "time": "2025-01-26T21:29:45+00:00"
        },
        {
            "name": "league/commonmark",
            "version": "2.6.1",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/commonmark.git",
                "reference": "d990688c91cedfb69753ffc2512727ec646df2ad"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/d990688c91cedfb69753ffc2512727ec646df2ad",
                "reference": "d990688c91cedfb69753ffc2512727ec646df2ad",
                "shasum": ""
            },
            "require": {
                "ext-mbstring": "*",
                "league/config": "^1.1.1",
                "php": "^7.4 || ^8.0",
                "psr/event-dispatcher": "^1.0",
                "symfony/deprecation-contracts": "^2.1 || ^3.0",
                "symfony/polyfill-php80": "^1.16"
            },
            "require-dev": {
                "cebe/markdown": "^1.0",
                "commonmark/cmark": "0.31.1",
                "commonmark/commonmark.js": "0.31.1",
                "composer/package-versions-deprecated": "^1.8",
                "embed/embed": "^4.4",
                "erusev/parsedown": "^1.0",
                "ext-json": "*",
                "github/gfm": "0.29.0",
                "michelf/php-markdown": "^1.4 || ^2.0",
                "nyholm/psr7": "^1.5",
                "phpstan/phpstan": "^1.8.2",
                "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0",
                "scrutinizer/ocular": "^1.8.1",
                "symfony/finder": "^5.3 | ^6.0 | ^7.0",
                "symfony/process": "^5.4 | ^6.0 | ^7.0",
                "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 | ^7.0",
                "unleashedtech/php-coding-standard": "^3.1.1",
                "vimeo/psalm": "^4.24.0 || ^5.0.0"
            },
            "suggest": {
                "symfony/yaml": "v2.3+ required if using the Front Matter extension"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "2.7-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "League\\CommonMark\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Colin O'Dell",
                    "email": "<EMAIL>",
                    "homepage": "https://www.colinodell.com",
                    "role": "Lead Developer"
                }
            ],
            "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)",
            "homepage": "https://commonmark.thephpleague.com",
            "keywords": [
                "commonmark",
                "flavored",
                "gfm",
                "github",
                "github-flavored",
                "markdown",
                "md",
                "parser"
            ],
            "support": {
                "docs": "https://commonmark.thephpleague.com/",
                "forum": "https://github.com/thephpleague/commonmark/discussions",
                "issues": "https://github.com/thephpleague/commonmark/issues",
                "rss": "https://github.com/thephpleague/commonmark/releases.atom",
                "source": "https://github.com/thephpleague/commonmark"
            },
            "funding": [
                {
                    "url": "https://www.colinodell.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://www.paypal.me/colinpodell/10.00",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/colinodell",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/league/commonmark",
                    "type": "tidelift"
                }
            ],
            "time": "2024-12-29T14:10:59+00:00"
        },
        {
            "name": "league/config",
            "version": "v1.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/config.git",
                "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3",
                "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3",
                "shasum": ""
            },
            "require": {
                "dflydev/dot-access-data": "^3.0.1",
                "nette/schema": "^1.2",
                "php": "^7.4 || ^8.0"
            },
            "require-dev": {
                "phpstan/phpstan": "^1.8.2",
                "phpunit/phpunit": "^9.5.5",
                "scrutinizer/ocular": "^1.8.1",
                "unleashedtech/php-coding-standard": "^3.1",
                "vimeo/psalm": "^4.7.3"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "1.2-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "League\\Config\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Colin O'Dell",
                    "email": "<EMAIL>",
                    "homepage": "https://www.colinodell.com",
                    "role": "Lead Developer"
                }
            ],
            "description": "Define configuration arrays with strict schemas and access values with dot notation",
            "homepage": "https://config.thephpleague.com",
            "keywords": [
                "array",
                "config",
                "configuration",
                "dot",
                "dot-access",
                "nested",
                "schema"
            ],
            "support": {
                "docs": "https://config.thephpleague.com/",
                "issues": "https://github.com/thephpleague/config/issues",
                "rss": "https://github.com/thephpleague/config/releases.atom",
                "source": "https://github.com/thephpleague/config"
            },
            "funding": [
                {
                    "url": "https://www.colinodell.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://www.paypal.me/colinpodell/10.00",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/colinodell",
                    "type": "github"
                }
            ],
            "time": "2022-12-11T20:36:23+00:00"
        },
        {
            "name": "league/flysystem",
            "version": "3.29.1",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/flysystem.git",
                "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/edc1bb7c86fab0776c3287dbd19b5fa278347319",
                "reference": "edc1bb7c86fab0776c3287dbd19b5fa278347319",
                "shasum": ""
            },
            "require": {
                "league/flysystem-local": "^3.0.0",
                "league/mime-type-detection": "^1.0.0",
                "php": "^8.0.2"
            },
            "conflict": {
                "async-aws/core": "<1.19.0",
                "async-aws/s3": "<1.14.0",
                "aws/aws-sdk-php": "3.209.31 || 3.210.0",
                "guzzlehttp/guzzle": "<7.0",
                "guzzlehttp/ringphp": "<1.1.1",
                "phpseclib/phpseclib": "3.0.15",
                "symfony/http-client": "<5.2"
            },
            "require-dev": {
                "async-aws/s3": "^1.5 || ^2.0",
                "async-aws/simple-s3": "^1.1 || ^2.0",
                "aws/aws-sdk-php": "^3.295.10",
                "composer/semver": "^3.0",
                "ext-fileinfo": "*",
                "ext-ftp": "*",
                "ext-mongodb": "^1.3",
                "ext-zip": "*",
                "friendsofphp/php-cs-fixer": "^3.5",
                "google/cloud-storage": "^1.23",
                "guzzlehttp/psr7": "^2.6",
                "microsoft/azure-storage-blob": "^1.1",
                "mongodb/mongodb": "^1.2",
                "phpseclib/phpseclib": "^3.0.36",
                "phpstan/phpstan": "^1.10",
                "phpunit/phpunit": "^9.5.11|^10.0",
                "sabre/dav": "^4.6.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "League\\Flysystem\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Frank de Jonge",
                    "email": "<EMAIL>"
                }
            ],
            "description": "File storage abstraction for PHP",
            "keywords": [
                "WebDAV",
                "aws",
                "cloud",
                "file",
                "files",
                "filesystem",
                "filesystems",
                "ftp",
                "s3",
                "sftp",
                "storage"
            ],
            "support": {
                "issues": "https://github.com/thephpleague/flysystem/issues",
                "source": "https://github.com/thephpleague/flysystem/tree/3.29.1"
            },
            "time": "2024-10-08T08:58:34+00:00"
        },
        {
            "name": "league/flysystem-local",
            "version": "3.29.0",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/flysystem-local.git",
                "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/e0e8d52ce4b2ed154148453d321e97c8e931bd27",
                "reference": "e0e8d52ce4b2ed154148453d321e97c8e931bd27",
                "shasum": ""
            },
            "require": {
                "ext-fileinfo": "*",
                "league/flysystem": "^3.0.0",
                "league/mime-type-detection": "^1.0.0",
                "php": "^8.0.2"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "League\\Flysystem\\Local\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Frank de Jonge",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Local filesystem adapter for Flysystem.",
            "keywords": [
                "Flysystem",
                "file",
                "files",
                "filesystem",
                "local"
            ],
            "support": {
                "source": "https://github.com/thephpleague/flysystem-local/tree/3.29.0"
            },
            "time": "2024-08-09T21:24:39+00:00"
        },
        {
            "name": "league/mime-type-detection",
            "version": "1.16.0",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/mime-type-detection.git",
                "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/2d6702ff215bf922936ccc1ad31007edc76451b9",
                "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9",
                "shasum": ""
            },
            "require": {
                "ext-fileinfo": "*",
                "php": "^7.4 || ^8.0"
            },
            "require-dev": {
                "friendsofphp/php-cs-fixer": "^3.2",
                "phpstan/phpstan": "^0.12.68",
                "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "League\\MimeTypeDetection\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Frank de Jonge",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Mime-type detection for Flysystem",
            "support": {
                "issues": "https://github.com/thephpleague/mime-type-detection/issues",
                "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"
            },
            "funding": [
                {
                    "url": "https://github.com/frankdejonge",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/league/flysystem",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-21T08:32:55+00:00"
        },
        {
            "name": "league/oauth1-client",
            "version": "v1.11.0",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/oauth1-client.git",
                "reference": "f9c94b088837eb1aae1ad7c4f23eb65cc6993055"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/oauth1-client/zipball/f9c94b088837eb1aae1ad7c4f23eb65cc6993055",
                "reference": "f9c94b088837eb1aae1ad7c4f23eb65cc6993055",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "ext-openssl": "*",
                "guzzlehttp/guzzle": "^6.0|^7.0",
                "guzzlehttp/psr7": "^1.7|^2.0",
                "php": ">=7.1||>=8.0"
            },
            "require-dev": {
                "ext-simplexml": "*",
                "friendsofphp/php-cs-fixer": "^2.17",
                "mockery/mockery": "^1.3.3",
                "phpstan/phpstan": "^0.12.42",
                "phpunit/phpunit": "^7.5||9.5"
            },
            "suggest": {
                "ext-simplexml": "For decoding XML-based responses."
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.0-dev",
                    "dev-develop": "2.0-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "League\\OAuth1\\Client\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Ben Corlett",
                    "email": "<EMAIL>",
                    "homepage": "http://www.webcomm.com.au",
                    "role": "Developer"
                }
            ],
            "description": "OAuth 1.0 Client Library",
            "keywords": [
                "Authentication",
                "SSO",
                "authorization",
                "bitbucket",
                "identity",
                "idp",
                "oauth",
                "oauth1",
                "single sign on",
                "trello",
                "tumblr",
                "twitter"
            ],
            "support": {
                "issues": "https://github.com/thephpleague/oauth1-client/issues",
                "source": "https://github.com/thephpleague/oauth1-client/tree/v1.11.0"
            },
            "time": "2024-12-10T19:59:05+00:00"
        },
        {
            "name": "league/uri",
            "version": "7.5.1",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/uri.git",
                "reference": "81fb5145d2644324614cc532b28efd0215bda430"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430",
                "reference": "81fb5145d2644324614cc532b28efd0215bda430",
                "shasum": ""
            },
            "require": {
                "league/uri-interfaces": "^7.5",
                "php": "^8.1"
            },
            "conflict": {
                "league/uri-schemes": "^1.0"
            },
            "suggest": {
                "ext-bcmath": "to improve IPV4 host parsing",
                "ext-fileinfo": "to create Data URI from file contennts",
                "ext-gmp": "to improve IPV4 host parsing",
                "ext-intl": "to handle IDN host with the best performance",
                "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain",
                "league/uri-components": "Needed to easily manipulate URI objects components",
                "php-64bit": "to improve IPV4 host parsing",
                "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "7.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "League\\Uri\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Ignace Nyamagana Butera",
                    "email": "<EMAIL>",
                    "homepage": "https://nyamsprod.com"
                }
            ],
            "description": "URI manipulation library",
            "homepage": "https://uri.thephpleague.com",
            "keywords": [
                "data-uri",
                "file-uri",
                "ftp",
                "hostname",
                "http",
                "https",
                "middleware",
                "parse_str",
                "parse_url",
                "psr-7",
                "query-string",
                "querystring",
                "rfc3986",
                "rfc3987",
                "rfc6570",
                "uri",
                "uri-template",
                "url",
                "ws"
            ],
            "support": {
                "docs": "https://uri.thephpleague.com",
                "forum": "https://thephpleague.slack.com",
                "issues": "https://github.com/thephpleague/uri-src/issues",
                "source": "https://github.com/thephpleague/uri/tree/7.5.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sponsors/nyamsprod",
                    "type": "github"
                }
            ],
            "time": "2024-12-08T08:40:02+00:00"
        },
        {
            "name": "league/uri-interfaces",
            "version": "7.5.0",
            "source": {
                "type": "git",
                "url": "https://github.com/thephpleague/uri-interfaces.git",
                "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742",
                "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742",
                "shasum": ""
            },
            "require": {
                "ext-filter": "*",
                "php": "^8.1",
                "psr/http-factory": "^1",
                "psr/http-message": "^1.1 || ^2.0"
            },
            "suggest": {
                "ext-bcmath": "to improve IPV4 host parsing",
                "ext-gmp": "to improve IPV4 host parsing",
                "ext-intl": "to handle IDN host with the best performance",
                "php-64bit": "to improve IPV4 host parsing",
                "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "7.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "League\\Uri\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Ignace Nyamagana Butera",
                    "email": "<EMAIL>",
                    "homepage": "https://nyamsprod.com"
                }
            ],
            "description": "Common interfaces and classes for URI representation and interaction",
            "homepage": "https://uri.thephpleague.com",
            "keywords": [
                "data-uri",
                "file-uri",
                "ftp",
                "hostname",
                "http",
                "https",
                "parse_str",
                "parse_url",
                "psr-7",
                "query-string",
                "querystring",
                "rfc3986",
                "rfc3987",
                "rfc6570",
                "uri",
                "url",
                "ws"
            ],
            "support": {
                "docs": "https://uri.thephpleague.com",
                "forum": "https://thephpleague.slack.com",
                "issues": "https://github.com/thephpleague/uri-src/issues",
                "source": "https://github.com/thephpleague/uri-interfaces/tree/7.5.0"
            },
            "funding": [
                {
                    "url": "https://github.com/sponsors/nyamsprod",
                    "type": "github"
                }
            ],
            "time": "2024-12-08T08:18:47+00:00"
        },
        {
            "name": "maatwebsite/excel",
            "version": "3.1.64",
            "source": {
                "type": "git",
                "url": "https://github.com/SpartnerNL/Laravel-Excel.git",
                "reference": "e25d44a2d91da9179cd2d7fec952313548597a79"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/SpartnerNL/Laravel-Excel/zipball/e25d44a2d91da9179cd2d7fec952313548597a79",
                "reference": "e25d44a2d91da9179cd2d7fec952313548597a79",
                "shasum": ""
            },
            "require": {
                "composer/semver": "^3.3",
                "ext-json": "*",
                "illuminate/support": "5.8.*||^6.0||^7.0||^8.0||^9.0||^10.0||^11.0||^12.0",
                "php": "^7.0||^8.0",
                "phpoffice/phpspreadsheet": "^1.29.9",
                "psr/simple-cache": "^1.0||^2.0||^3.0"
            },
            "require-dev": {
                "laravel/scout": "^7.0||^8.0||^9.0||^10.0",
                "orchestra/testbench": "^6.0||^7.0||^8.0||^9.0||^10.0",
                "predis/predis": "^1.1"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "aliases": {
                        "Excel": "Maatwebsite\\Excel\\Facades\\Excel"
                    },
                    "providers": [
                        "Maatwebsite\\Excel\\ExcelServiceProvider"
                    ]
                }
            },
            "autoload": {
                "psr-4": {
                    "Maatwebsite\\Excel\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Patrick Brouwers",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Supercharged Excel exports and imports in Laravel",
            "keywords": [
                "PHPExcel",
                "batch",
                "csv",
                "excel",
                "export",
                "import",
                "laravel",
                "php",
                "phpspreadsheet"
            ],
            "support": {
                "issues": "https://github.com/SpartnerNL/Laravel-Excel/issues",
                "source": "https://github.com/SpartnerNL/Laravel-Excel/tree/3.1.64"
            },
            "funding": [
                {
                    "url": "https://laravel-excel.com/commercial-support",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/patrickbrouwers",
                    "type": "github"
                }
            ],
            "time": "2025-02-24T11:12:50+00:00"
        },
        {
            "name": "maennchen/zipstream-php",
            "version": "3.1.2",
            "source": {
                "type": "git",
                "url": "https://github.com/maennchen/ZipStream-PHP.git",
                "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/aeadcf5c412332eb426c0f9b4485f6accba2a99f",
                "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f",
                "shasum": ""
            },
            "require": {
                "ext-mbstring": "*",
                "ext-zlib": "*",
                "php-64bit": "^8.2"
            },
            "require-dev": {
                "brianium/paratest": "^7.7",
                "ext-zip": "*",
                "friendsofphp/php-cs-fixer": "^3.16",
                "guzzlehttp/guzzle": "^7.5",
                "mikey179/vfsstream": "^1.6",
                "php-coveralls/php-coveralls": "^2.5",
                "phpunit/phpunit": "^11.0",
                "vimeo/psalm": "^6.0"
            },
            "suggest": {
                "guzzlehttp/psr7": "^2.4",
                "psr/http-message": "^2.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "ZipStream\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Paul Duncan",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jonatan Männchen",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jesse Donat",
                    "email": "<EMAIL>"
                },
                {
                    "name": "András Kolesár",
                    "email": "<EMAIL>"
                }
            ],
            "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.",
            "keywords": [
                "stream",
                "zip"
            ],
            "support": {
                "issues": "https://github.com/maennchen/ZipStream-PHP/issues",
                "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.2"
            },
            "funding": [
                {
                    "url": "https://github.com/maennchen",
                    "type": "github"
                }
            ],
            "time": "2025-01-27T12:07:53+00:00"
        },
        {
            "name": "markbaker/complex",
            "version": "3.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/MarkBaker/PHPComplex.git",
                "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9",
                "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9",
                "shasum": ""
            },
            "require": {
                "php": "^7.2 || ^8.0"
            },
            "require-dev": {
                "dealerdirect/phpcodesniffer-composer-installer": "dev-master",
                "phpcompatibility/php-compatibility": "^9.3",
                "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0",
                "squizlabs/php_codesniffer": "^3.7"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Complex\\": "classes/src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Mark Baker",
                    "email": "<EMAIL>"
                }
            ],
            "description": "PHP Class for working with complex numbers",
            "homepage": "https://github.com/MarkBaker/PHPComplex",
            "keywords": [
                "complex",
                "mathematics"
            ],
            "support": {
                "issues": "https://github.com/MarkBaker/PHPComplex/issues",
                "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"
            },
            "time": "2022-12-06T16:21:08+00:00"
        },
        {
            "name": "markbaker/matrix",
            "version": "3.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/MarkBaker/PHPMatrix.git",
                "reference": "728434227fe21be27ff6d86621a1b13107a2562c"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c",
                "reference": "728434227fe21be27ff6d86621a1b13107a2562c",
                "shasum": ""
            },
            "require": {
                "php": "^7.1 || ^8.0"
            },
            "require-dev": {
                "dealerdirect/phpcodesniffer-composer-installer": "dev-master",
                "phpcompatibility/php-compatibility": "^9.3",
                "phpdocumentor/phpdocumentor": "2.*",
                "phploc/phploc": "^4.0",
                "phpmd/phpmd": "2.*",
                "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0",
                "sebastian/phpcpd": "^4.0",
                "squizlabs/php_codesniffer": "^3.7"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Matrix\\": "classes/src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Mark Baker",
                    "email": "<EMAIL>"
                }
            ],
            "description": "PHP Class for working with matrices",
            "homepage": "https://github.com/MarkBaker/PHPMatrix",
            "keywords": [
                "mathematics",
                "matrix",
                "vector"
            ],
            "support": {
                "issues": "https://github.com/MarkBaker/PHPMatrix/issues",
                "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"
            },
            "time": "2022-12-02T22:17:43+00:00"
        },
        {
            "name": "monolog/monolog",
            "version": "3.8.1",
            "source": {
                "type": "git",
                "url": "https://github.com/Seldaek/monolog.git",
                "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/Seldaek/monolog/zipball/aef6ee73a77a66e404dd6540934a9ef1b3c855b4",
                "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4",
                "shasum": ""
            },
            "require": {
                "php": ">=8.1",
                "psr/log": "^2.0 || ^3.0"
            },
            "provide": {
                "psr/log-implementation": "3.0.0"
            },
            "require-dev": {
                "aws/aws-sdk-php": "^3.0",
                "doctrine/couchdb": "~1.0@dev",
                "elasticsearch/elasticsearch": "^7 || ^8",
                "ext-json": "*",
                "graylog2/gelf-php": "^1.4.2 || ^2.0",
                "guzzlehttp/guzzle": "^7.4.5",
                "guzzlehttp/psr7": "^2.2",
                "mongodb/mongodb": "^1.8",
                "php-amqplib/php-amqplib": "~2.4 || ^3",
                "php-console/php-console": "^3.1.8",
                "phpstan/phpstan": "^2",
                "phpstan/phpstan-deprecation-rules": "^2",
                "phpstan/phpstan-strict-rules": "^2",
                "phpunit/phpunit": "^10.5.17 || ^11.0.7",
                "predis/predis": "^1.1 || ^2",
                "rollbar/rollbar": "^4.0",
                "ruflin/elastica": "^7 || ^8",
                "symfony/mailer": "^5.4 || ^6",
                "symfony/mime": "^5.4 || ^6"
            },
            "suggest": {
                "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB",
                "doctrine/couchdb": "Allow sending log messages to a CouchDB server",
                "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client",
                "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)",
                "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler",
                "ext-mbstring": "Allow to work properly with unicode symbols",
                "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)",
                "ext-openssl": "Required to send log messages using SSL",
                "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)",
                "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server",
                "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)",
                "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib",
                "rollbar/rollbar": "Allow sending log messages to Rollbar",
                "ruflin/elastica": "Allow sending log messages to an Elastic Search server"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Monolog\\": "src/Monolog"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Jordi Boggiano",
                    "email": "<EMAIL>",
                    "homepage": "https://seld.be"
                }
            ],
            "description": "Sends your logs to files, sockets, inboxes, databases and various web services",
            "homepage": "https://github.com/Seldaek/monolog",
            "keywords": [
                "log",
                "logging",
                "psr-3"
            ],
            "support": {
                "issues": "https://github.com/Seldaek/monolog/issues",
                "source": "https://github.com/Seldaek/monolog/tree/3.8.1"
            },
            "funding": [
                {
                    "url": "https://github.com/Seldaek",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/monolog/monolog",
                    "type": "tidelift"
                }
            ],
            "time": "2024-12-05T17:15:07+00:00"
        },
        {
            "name": "mpociot/reflection-docblock",
            "version": "1.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/mpociot/reflection-docblock.git",
                "reference": "c8b2e2b1f5cebbb06e2b5ccbf2958f2198867587"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/mpociot/reflection-docblock/zipball/c8b2e2b1f5cebbb06e2b5ccbf2958f2198867587",
                "reference": "c8b2e2b1f5cebbb06e2b5ccbf2958f2198867587",
                "shasum": ""
            },
            "require": {
                "php": ">=5.3.3"
            },
            "require-dev": {
                "phpunit/phpunit": "~4.0"
            },
            "suggest": {
                "dflydev/markdown": "~1.0",
                "erusev/parsedown": "~1.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.0.x-dev"
                }
            },
            "autoload": {
                "psr-0": {
                    "Mpociot": [
                        "src/"
                    ]
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Mike van Riel",
                    "email": "<EMAIL>"
                }
            ],
            "support": {
                "issues": "https://github.com/mpociot/reflection-docblock/issues",
                "source": "https://github.com/mpociot/reflection-docblock/tree/master"
            },
            "time": "2016-06-20T20:53:12+00:00"
        },
        {
            "name": "nesbot/carbon",
            "version": "3.8.6",
            "source": {
                "type": "git",
                "url": "https://github.com/CarbonPHP/carbon.git",
                "reference": "ff2f20cf83bd4d503720632ce8a426dc747bf7fd"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ff2f20cf83bd4d503720632ce8a426dc747bf7fd",
                "reference": "ff2f20cf83bd4d503720632ce8a426dc747bf7fd",
                "shasum": ""
            },
            "require": {
                "carbonphp/carbon-doctrine-types": "<100.0",
                "ext-json": "*",
                "php": "^8.1",
                "psr/clock": "^1.0",
                "symfony/clock": "^6.3 || ^7.0",
                "symfony/polyfill-mbstring": "^1.0",
                "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"
            },
            "provide": {
                "psr/clock-implementation": "1.0"
            },
            "require-dev": {
                "doctrine/dbal": "^3.6.3 || ^4.0",
                "doctrine/orm": "^2.15.2 || ^3.0",
                "friendsofphp/php-cs-fixer": "^3.57.2",
                "kylekatarnls/multi-tester": "^2.5.3",
                "ondrejmirtes/better-reflection": "^********",
                "phpmd/phpmd": "^2.15.0",
                "phpstan/extension-installer": "^1.3.1",
                "phpstan/phpstan": "^1.11.2",
                "phpunit/phpunit": "^10.5.20",
                "squizlabs/php_codesniffer": "^3.9.0"
            },
            "bin": [
                "bin/carbon"
            ],
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Carbon\\Laravel\\ServiceProvider"
                    ]
                },
                "phpstan": {
                    "includes": [
                        "extension.neon"
                    ]
                },
                "branch-alias": {
                    "dev-2.x": "2.x-dev",
                    "dev-master": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Carbon\\": "src/Carbon/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Brian Nesbitt",
                    "email": "<EMAIL>",
                    "homepage": "https://markido.com"
                },
                {
                    "name": "kylekatarnls",
                    "homepage": "https://github.com/kylekatarnls"
                }
            ],
            "description": "An API extension for DateTime that supports 281 different languages.",
            "homepage": "https://carbon.nesbot.com",
            "keywords": [
                "date",
                "datetime",
                "time"
            ],
            "support": {
                "docs": "https://carbon.nesbot.com/docs",
                "issues": "https://github.com/CarbonPHP/carbon/issues",
                "source": "https://github.com/CarbonPHP/carbon"
            },
            "funding": [
                {
                    "url": "https://github.com/sponsors/kylekatarnls",
                    "type": "github"
                },
                {
                    "url": "https://opencollective.com/Carbon#sponsor",
                    "type": "opencollective"
                },
                {
                    "url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-20T17:33:38+00:00"
        },
        {
            "name": "nette/schema",
            "version": "v1.3.2",
            "source": {
                "type": "git",
                "url": "https://github.com/nette/schema.git",
                "reference": "da801d52f0354f70a638673c4a0f04e16529431d"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/nette/schema/zipball/da801d52f0354f70a638673c4a0f04e16529431d",
                "reference": "da801d52f0354f70a638673c4a0f04e16529431d",
                "shasum": ""
            },
            "require": {
                "nette/utils": "^4.0",
                "php": "8.1 - 8.4"
            },
            "require-dev": {
                "nette/tester": "^2.5.2",
                "phpstan/phpstan-nette": "^1.0",
                "tracy/tracy": "^2.8"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.3-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause",
                "GPL-2.0-only",
                "GPL-3.0-only"
            ],
            "authors": [
                {
                    "name": "David Grudl",
                    "homepage": "https://davidgrudl.com"
                },
                {
                    "name": "Nette Community",
                    "homepage": "https://nette.org/contributors"
                }
            ],
            "description": "📐 Nette Schema: validating data structures against a given Schema.",
            "homepage": "https://nette.org",
            "keywords": [
                "config",
                "nette"
            ],
            "support": {
                "issues": "https://github.com/nette/schema/issues",
                "source": "https://github.com/nette/schema/tree/v1.3.2"
            },
            "time": "2024-10-06T23:10:23+00:00"
        },
        {
            "name": "nette/utils",
            "version": "v4.0.5",
            "source": {
                "type": "git",
                "url": "https://github.com/nette/utils.git",
                "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/nette/utils/zipball/736c567e257dbe0fcf6ce81b4d6dbe05c6899f96",
                "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96",
                "shasum": ""
            },
            "require": {
                "php": "8.0 - 8.4"
            },
            "conflict": {
                "nette/finder": "<3",
                "nette/schema": "<1.2.2"
            },
            "require-dev": {
                "jetbrains/phpstorm-attributes": "dev-master",
                "nette/tester": "^2.5",
                "phpstan/phpstan": "^1.0",
                "tracy/tracy": "^2.9"
            },
            "suggest": {
                "ext-gd": "to use Image",
                "ext-iconv": "to use Strings::webalize(), toAscii(), chr() and reverse()",
                "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()",
                "ext-json": "to use Nette\\Utils\\Json",
                "ext-mbstring": "to use Strings::lower() etc...",
                "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "4.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause",
                "GPL-2.0-only",
                "GPL-3.0-only"
            ],
            "authors": [
                {
                    "name": "David Grudl",
                    "homepage": "https://davidgrudl.com"
                },
                {
                    "name": "Nette Community",
                    "homepage": "https://nette.org/contributors"
                }
            ],
            "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.",
            "homepage": "https://nette.org",
            "keywords": [
                "array",
                "core",
                "datetime",
                "images",
                "json",
                "nette",
                "paginator",
                "password",
                "slugify",
                "string",
                "unicode",
                "utf-8",
                "utility",
                "validation"
            ],
            "support": {
                "issues": "https://github.com/nette/utils/issues",
                "source": "https://github.com/nette/utils/tree/v4.0.5"
            },
            "time": "2024-08-07T15:39:19+00:00"
        },
        {
            "name": "nikic/php-parser",
            "version": "v5.4.0",
            "source": {
                "type": "git",
                "url": "https://github.com/nikic/PHP-Parser.git",
                "reference": "447a020a1f875a434d62f2a401f53b82a396e494"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494",
                "reference": "447a020a1f875a434d62f2a401f53b82a396e494",
                "shasum": ""
            },
            "require": {
                "ext-ctype": "*",
                "ext-json": "*",
                "ext-tokenizer": "*",
                "php": ">=7.4"
            },
            "require-dev": {
                "ircmaxell/php-yacc": "^0.0.7",
                "phpunit/phpunit": "^9.0"
            },
            "bin": [
                "bin/php-parse"
            ],
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "5.0-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "PhpParser\\": "lib/PhpParser"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Nikita Popov"
                }
            ],
            "description": "A PHP parser written in PHP",
            "keywords": [
                "parser",
                "php"
            ],
            "support": {
                "issues": "https://github.com/nikic/PHP-Parser/issues",
                "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"
            },
            "time": "2024-12-30T11:07:19+00:00"
        },
        {
            "name": "nunomaduro/collision",
            "version": "v8.6.1",
            "source": {
                "type": "git",
                "url": "https://github.com/nunomaduro/collision.git",
                "reference": "86f003c132143d5a2ab214e19933946409e0cae7"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/nunomaduro/collision/zipball/86f003c132143d5a2ab214e19933946409e0cae7",
                "reference": "86f003c132143d5a2ab214e19933946409e0cae7",
                "shasum": ""
            },
            "require": {
                "filp/whoops": "^2.16.0",
                "nunomaduro/termwind": "^2.3.0",
                "php": "^8.2.0",
                "symfony/console": "^7.2.1"
            },
            "conflict": {
                "laravel/framework": "<11.39.1 || >=13.0.0",
                "phpunit/phpunit": "<11.5.3 || >=12.0.0"
            },
            "require-dev": {
                "larastan/larastan": "^2.9.12",
                "laravel/framework": "^11.39.1",
                "laravel/pint": "^1.20.0",
                "laravel/sail": "^1.40.0",
                "laravel/sanctum": "^4.0.7",
                "laravel/tinker": "^2.10.0",
                "orchestra/testbench-core": "^9.9.2",
                "pestphp/pest": "^3.7.3",
                "sebastian/environment": "^6.1.0 || ^7.2.0"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-8.x": "8.x-dev"
                }
            },
            "autoload": {
                "files": [
                    "./src/Adapters/Phpunit/Autoload.php"
                ],
                "psr-4": {
                    "NunoMaduro\\Collision\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nuno Maduro",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Cli error handling for console/command-line PHP applications.",
            "keywords": [
                "artisan",
                "cli",
                "command-line",
                "console",
                "dev",
                "error",
                "handling",
                "laravel",
                "laravel-zero",
                "php",
                "symfony"
            ],
            "support": {
                "issues": "https://github.com/nunomaduro/collision/issues",
                "source": "https://github.com/nunomaduro/collision"
            },
            "funding": [
                {
                    "url": "https://www.paypal.com/paypalme/enunomaduro",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/nunomaduro",
                    "type": "github"
                },
                {
                    "url": "https://www.patreon.com/nunomaduro",
                    "type": "patreon"
                }
            ],
            "time": "2025-01-23T13:41:43+00:00"
        },
        {
            "name": "nunomaduro/termwind",
            "version": "v2.3.0",
            "source": {
                "type": "git",
                "url": "https://github.com/nunomaduro/termwind.git",
                "reference": "52915afe6a1044e8b9cee1bcff836fb63acf9cda"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/52915afe6a1044e8b9cee1bcff836fb63acf9cda",
                "reference": "52915afe6a1044e8b9cee1bcff836fb63acf9cda",
                "shasum": ""
            },
            "require": {
                "ext-mbstring": "*",
                "php": "^8.2",
                "symfony/console": "^7.1.8"
            },
            "require-dev": {
                "illuminate/console": "^11.33.2",
                "laravel/pint": "^1.18.2",
                "mockery/mockery": "^1.6.12",
                "pestphp/pest": "^2.36.0",
                "phpstan/phpstan": "^1.12.11",
                "phpstan/phpstan-strict-rules": "^1.6.1",
                "symfony/var-dumper": "^7.1.8",
                "thecodingmachine/phpstan-strict-rules": "^1.0.0"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Termwind\\Laravel\\TermwindServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-2.x": "2.x-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/Functions.php"
                ],
                "psr-4": {
                    "Termwind\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nuno Maduro",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Its like Tailwind CSS, but for the console.",
            "keywords": [
                "cli",
                "console",
                "css",
                "package",
                "php",
                "style"
            ],
            "support": {
                "issues": "https://github.com/nunomaduro/termwind/issues",
                "source": "https://github.com/nunomaduro/termwind/tree/v2.3.0"
            },
            "funding": [
                {
                    "url": "https://www.paypal.com/paypalme/enunomaduro",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/nunomaduro",
                    "type": "github"
                },
                {
                    "url": "https://github.com/xiCO2k",
                    "type": "github"
                }
            ],
            "time": "2024-11-21T10:39:51+00:00"
        },
        {
            "name": "paragonie/constant_time_encoding",
            "version": "v3.0.0",
            "source": {
                "type": "git",
                "url": "https://github.com/paragonie/constant_time_encoding.git",
                "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512",
                "reference": "df1e7fde177501eee2037dd159cf04f5f301a512",
                "shasum": ""
            },
            "require": {
                "php": "^8"
            },
            "require-dev": {
                "phpunit/phpunit": "^9",
                "vimeo/psalm": "^4|^5"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "ParagonIE\\ConstantTime\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Paragon Initiative Enterprises",
                    "email": "<EMAIL>",
                    "homepage": "https://paragonie.com",
                    "role": "Maintainer"
                },
                {
                    "name": "Steve 'Sc00bz' Thomas",
                    "email": "<EMAIL>",
                    "homepage": "https://www.tobtu.com",
                    "role": "Original Developer"
                }
            ],
            "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)",
            "keywords": [
                "base16",
                "base32",
                "base32_decode",
                "base32_encode",
                "base64",
                "base64_decode",
                "base64_encode",
                "bin2hex",
                "encoding",
                "hex",
                "hex2bin",
                "rfc4648"
            ],
            "support": {
                "email": "<EMAIL>",
                "issues": "https://github.com/paragonie/constant_time_encoding/issues",
                "source": "https://github.com/paragonie/constant_time_encoding"
            },
            "time": "2024-05-08T12:36:18+00:00"
        },
        {
            "name": "paragonie/random_compat",
            "version": "v9.99.100",
            "source": {
                "type": "git",
                "url": "https://github.com/paragonie/random_compat.git",
                "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a",
                "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a",
                "shasum": ""
            },
            "require": {
                "php": ">= 7"
            },
            "require-dev": {
                "phpunit/phpunit": "4.*|5.*",
                "vimeo/psalm": "^1"
            },
            "suggest": {
                "ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."
            },
            "type": "library",
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Paragon Initiative Enterprises",
                    "email": "<EMAIL>",
                    "homepage": "https://paragonie.com"
                }
            ],
            "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7",
            "keywords": [
                "csprng",
                "polyfill",
                "pseudorandom",
                "random"
            ],
            "support": {
                "email": "<EMAIL>",
                "issues": "https://github.com/paragonie/random_compat/issues",
                "source": "https://github.com/paragonie/random_compat"
            },
            "time": "2020-10-15T08:29:30+00:00"
        },
        {
            "name": "phpoffice/phpspreadsheet",
            "version": "1.29.10",
            "source": {
                "type": "git",
                "url": "https://github.com/PHPOffice/PhpSpreadsheet.git",
                "reference": "c80041b1628c4f18030407134fe88303661d4e4e"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/c80041b1628c4f18030407134fe88303661d4e4e",
                "reference": "c80041b1628c4f18030407134fe88303661d4e4e",
                "shasum": ""
            },
            "require": {
                "composer/pcre": "^1||^2||^3",
                "ext-ctype": "*",
                "ext-dom": "*",
                "ext-fileinfo": "*",
                "ext-gd": "*",
                "ext-iconv": "*",
                "ext-libxml": "*",
                "ext-mbstring": "*",
                "ext-simplexml": "*",
                "ext-xml": "*",
                "ext-xmlreader": "*",
                "ext-xmlwriter": "*",
                "ext-zip": "*",
                "ext-zlib": "*",
                "ezyang/htmlpurifier": "^4.15",
                "maennchen/zipstream-php": "^2.1 || ^3.0",
                "markbaker/complex": "^3.0",
                "markbaker/matrix": "^3.0",
                "php": "^7.4 || ^8.0",
                "psr/http-client": "^1.0",
                "psr/http-factory": "^1.0",
                "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"
            },
            "require-dev": {
                "dealerdirect/phpcodesniffer-composer-installer": "dev-main",
                "dompdf/dompdf": "^1.0 || ^2.0 || ^3.0",
                "friendsofphp/php-cs-fixer": "^3.2",
                "mitoteam/jpgraph": "^10.3",
                "mpdf/mpdf": "^8.1.1",
                "phpcompatibility/php-compatibility": "^9.3",
                "phpstan/phpstan": "^1.1",
                "phpstan/phpstan-phpunit": "^1.0",
                "phpunit/phpunit": "^8.5 || ^9.0",
                "squizlabs/php_codesniffer": "^3.7",
                "tecnickcom/tcpdf": "^6.5"
            },
            "suggest": {
                "dompdf/dompdf": "Option for rendering PDF with PDF Writer",
                "ext-intl": "PHP Internationalization Functions",
                "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers",
                "mpdf/mpdf": "Option for rendering PDF with PDF Writer",
                "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Maarten Balliauw",
                    "homepage": "https://blog.maartenballiauw.be"
                },
                {
                    "name": "Mark Baker",
                    "homepage": "https://markbakeruk.net"
                },
                {
                    "name": "Franck Lefevre",
                    "homepage": "https://rootslabs.net"
                },
                {
                    "name": "Erik Tilt"
                },
                {
                    "name": "Adrien Crivelli"
                }
            ],
            "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine",
            "homepage": "https://github.com/PHPOffice/PhpSpreadsheet",
            "keywords": [
                "OpenXML",
                "excel",
                "gnumeric",
                "ods",
                "php",
                "spreadsheet",
                "xls",
                "xlsx"
            ],
            "support": {
                "issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues",
                "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.29.10"
            },
            "time": "2025-02-08T02:56:14+00:00"
        },
        {
            "name": "phpoption/phpoption",
            "version": "1.9.3",
            "source": {
                "type": "git",
                "url": "https://github.com/schmittjoh/php-option.git",
                "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54",
                "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54",
                "shasum": ""
            },
            "require": {
                "php": "^7.2.5 || ^8.0"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.8.2",
                "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"
            },
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": true,
                    "forward-command": false
                },
                "branch-alias": {
                    "dev-master": "1.9-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "PhpOption\\": "src/PhpOption/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "Apache-2.0"
            ],
            "authors": [
                {
                    "name": "Johannes M. Schmitt",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/schmittjoh"
                },
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                }
            ],
            "description": "Option Type for PHP",
            "keywords": [
                "language",
                "option",
                "php",
                "type"
            ],
            "support": {
                "issues": "https://github.com/schmittjoh/php-option/issues",
                "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption",
                    "type": "tidelift"
                }
            ],
            "time": "2024-07-20T21:41:07+00:00"
        },
        {
            "name": "phpseclib/phpseclib",
            "version": "3.0.43",
            "source": {
                "type": "git",
                "url": "https://github.com/phpseclib/phpseclib.git",
                "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02",
                "reference": "709ec107af3cb2f385b9617be72af8cf62441d02",
                "shasum": ""
            },
            "require": {
                "paragonie/constant_time_encoding": "^1|^2|^3",
                "paragonie/random_compat": "^1.4|^2.0|^9.99.99",
                "php": ">=5.6.1"
            },
            "require-dev": {
                "phpunit/phpunit": "*"
            },
            "suggest": {
                "ext-dom": "Install the DOM extension to load XML formatted public keys.",
                "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.",
                "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.",
                "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.",
                "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."
            },
            "type": "library",
            "autoload": {
                "files": [
                    "phpseclib/bootstrap.php"
                ],
                "psr-4": {
                    "phpseclib3\\": "phpseclib/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Jim Wigginton",
                    "email": "<EMAIL>",
                    "role": "Lead Developer"
                },
                {
                    "name": "Patrick Monnerat",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Andreas Fischer",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Hans-Jürgen Petrich",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.",
            "homepage": "http://phpseclib.sourceforge.net",
            "keywords": [
                "BigInteger",
                "aes",
                "asn.1",
                "asn1",
                "blowfish",
                "crypto",
                "cryptography",
                "encryption",
                "rsa",
                "security",
                "sftp",
                "signature",
                "signing",
                "ssh",
                "twofish",
                "x.509",
                "x509"
            ],
            "support": {
                "issues": "https://github.com/phpseclib/phpseclib/issues",
                "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"
            },
            "funding": [
                {
                    "url": "https://github.com/terrafrost",
                    "type": "github"
                },
                {
                    "url": "https://www.patreon.com/phpseclib",
                    "type": "patreon"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib",
                    "type": "tidelift"
                }
            ],
            "time": "2024-12-14T21:12:59+00:00"
        },
        {
            "name": "psr/clock",
            "version": "1.0.0",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/clock.git",
                "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d",
                "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d",
                "shasum": ""
            },
            "require": {
                "php": "^7.0 || ^8.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Psr\\Clock\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "Common interface for reading the clock.",
            "homepage": "https://github.com/php-fig/clock",
            "keywords": [
                "clock",
                "now",
                "psr",
                "psr-20",
                "time"
            ],
            "support": {
                "issues": "https://github.com/php-fig/clock/issues",
                "source": "https://github.com/php-fig/clock/tree/1.0.0"
            },
            "time": "2022-11-25T14:36:26+00:00"
        },
        {
            "name": "psr/container",
            "version": "2.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/container.git",
                "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963",
                "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963",
                "shasum": ""
            },
            "require": {
                "php": ">=7.4.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\Container\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "Common Container Interface (PHP FIG PSR-11)",
            "homepage": "https://github.com/php-fig/container",
            "keywords": [
                "PSR-11",
                "container",
                "container-interface",
                "container-interop",
                "psr"
            ],
            "support": {
                "issues": "https://github.com/php-fig/container/issues",
                "source": "https://github.com/php-fig/container/tree/2.0.2"
            },
            "time": "2021-11-05T16:47:00+00:00"
        },
        {
            "name": "psr/event-dispatcher",
            "version": "1.0.0",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/event-dispatcher.git",
                "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0",
                "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\EventDispatcher\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "http://www.php-fig.org/"
                }
            ],
            "description": "Standard interfaces for event handling.",
            "keywords": [
                "events",
                "psr",
                "psr-14"
            ],
            "support": {
                "issues": "https://github.com/php-fig/event-dispatcher/issues",
                "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"
            },
            "time": "2019-01-08T18:20:26+00:00"
        },
        {
            "name": "psr/http-client",
            "version": "1.0.3",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/http-client.git",
                "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90",
                "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90",
                "shasum": ""
            },
            "require": {
                "php": "^7.0 || ^8.0",
                "psr/http-message": "^1.0 || ^2.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\Http\\Client\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "Common interface for HTTP clients",
            "homepage": "https://github.com/php-fig/http-client",
            "keywords": [
                "http",
                "http-client",
                "psr",
                "psr-18"
            ],
            "support": {
                "source": "https://github.com/php-fig/http-client"
            },
            "time": "2023-09-23T14:17:50+00:00"
        },
        {
            "name": "psr/http-factory",
            "version": "1.1.0",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/http-factory.git",
                "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a",
                "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a",
                "shasum": ""
            },
            "require": {
                "php": ">=7.1",
                "psr/http-message": "^1.0 || ^2.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\Http\\Message\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories",
            "keywords": [
                "factory",
                "http",
                "message",
                "psr",
                "psr-17",
                "psr-7",
                "request",
                "response"
            ],
            "support": {
                "source": "https://github.com/php-fig/http-factory"
            },
            "time": "2024-04-15T12:06:14+00:00"
        },
        {
            "name": "psr/http-message",
            "version": "2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/http-message.git",
                "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71",
                "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71",
                "shasum": ""
            },
            "require": {
                "php": "^7.2 || ^8.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\Http\\Message\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "Common interface for HTTP messages",
            "homepage": "https://github.com/php-fig/http-message",
            "keywords": [
                "http",
                "http-message",
                "psr",
                "psr-7",
                "request",
                "response"
            ],
            "support": {
                "source": "https://github.com/php-fig/http-message/tree/2.0"
            },
            "time": "2023-04-04T09:54:51+00:00"
        },
        {
            "name": "psr/log",
            "version": "3.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/log.git",
                "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3",
                "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3",
                "shasum": ""
            },
            "require": {
                "php": ">=8.0.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "3.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\Log\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "Common interface for logging libraries",
            "homepage": "https://github.com/php-fig/log",
            "keywords": [
                "log",
                "psr",
                "psr-3"
            ],
            "support": {
                "source": "https://github.com/php-fig/log/tree/3.0.2"
            },
            "time": "2024-09-11T13:17:53+00:00"
        },
        {
            "name": "psr/simple-cache",
            "version": "3.0.0",
            "source": {
                "type": "git",
                "url": "https://github.com/php-fig/simple-cache.git",
                "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865",
                "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865",
                "shasum": ""
            },
            "require": {
                "php": ">=8.0.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "3.0.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Psr\\SimpleCache\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "PHP-FIG",
                    "homepage": "https://www.php-fig.org/"
                }
            ],
            "description": "Common interfaces for simple caching",
            "keywords": [
                "cache",
                "caching",
                "psr",
                "psr-16",
                "simple-cache"
            ],
            "support": {
                "source": "https://github.com/php-fig/simple-cache/tree/3.0.0"
            },
            "time": "2021-10-29T13:26:27+00:00"
        },
        {
            "name": "psy/psysh",
            "version": "v0.12.7",
            "source": {
                "type": "git",
                "url": "https://github.com/bobthecow/psysh.git",
                "reference": "d73fa3c74918ef4522bb8a3bf9cab39161c4b57c"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/bobthecow/psysh/zipball/d73fa3c74918ef4522bb8a3bf9cab39161c4b57c",
                "reference": "d73fa3c74918ef4522bb8a3bf9cab39161c4b57c",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "ext-tokenizer": "*",
                "nikic/php-parser": "^5.0 || ^4.0",
                "php": "^8.0 || ^7.4",
                "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4",
                "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"
            },
            "conflict": {
                "symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.2"
            },
            "suggest": {
                "ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)",
                "ext-pdo-sqlite": "The doc command requires SQLite to work.",
                "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."
            },
            "bin": [
                "bin/psysh"
            ],
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": false,
                    "forward-command": false
                },
                "branch-alias": {
                    "dev-main": "0.12.x-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/functions.php"
                ],
                "psr-4": {
                    "Psy\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Justin Hileman",
                    "email": "<EMAIL>",
                    "homepage": "http://justinhileman.com"
                }
            ],
            "description": "An interactive shell for modern PHP.",
            "homepage": "http://psysh.org",
            "keywords": [
                "REPL",
                "console",
                "interactive",
                "shell"
            ],
            "support": {
                "issues": "https://github.com/bobthecow/psysh/issues",
                "source": "https://github.com/bobthecow/psysh/tree/v0.12.7"
            },
            "time": "2024-12-10T01:58:33+00:00"
        },
        {
            "name": "ralouphie/getallheaders",
            "version": "3.0.3",
            "source": {
                "type": "git",
                "url": "https://github.com/ralouphie/getallheaders.git",
                "reference": "120b605dfeb996808c31b6477290a714d356e822"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822",
                "reference": "120b605dfeb996808c31b6477290a714d356e822",
                "shasum": ""
            },
            "require": {
                "php": ">=5.6"
            },
            "require-dev": {
                "php-coveralls/php-coveralls": "^2.1",
                "phpunit/phpunit": "^5 || ^6.5"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "src/getallheaders.php"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Ralph Khattar",
                    "email": "<EMAIL>"
                }
            ],
            "description": "A polyfill for getallheaders.",
            "support": {
                "issues": "https://github.com/ralouphie/getallheaders/issues",
                "source": "https://github.com/ralouphie/getallheaders/tree/develop"
            },
            "time": "2019-03-08T08:55:37+00:00"
        },
        {
            "name": "ramsey/collection",
            "version": "2.1.0",
            "source": {
                "type": "git",
                "url": "https://github.com/ramsey/collection.git",
                "reference": "3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/ramsey/collection/zipball/3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109",
                "reference": "3c5990b8a5e0b79cd1cf11c2dc1229e58e93f109",
                "shasum": ""
            },
            "require": {
                "php": "^8.1"
            },
            "require-dev": {
                "captainhook/plugin-composer": "^5.3",
                "ergebnis/composer-normalize": "^2.45",
                "fakerphp/faker": "^1.24",
                "hamcrest/hamcrest-php": "^2.0",
                "jangregor/phpstan-prophecy": "^2.1",
                "mockery/mockery": "^1.6",
                "php-parallel-lint/php-console-highlighter": "^1.0",
                "php-parallel-lint/php-parallel-lint": "^1.4",
                "phpspec/prophecy-phpunit": "^2.3",
                "phpstan/extension-installer": "^1.4",
                "phpstan/phpstan": "^2.1",
                "phpstan/phpstan-mockery": "^2.0",
                "phpstan/phpstan-phpunit": "^2.0",
                "phpunit/phpunit": "^10.5",
                "ramsey/coding-standard": "^2.3",
                "ramsey/conventional-commits": "^1.6",
                "roave/security-advisories": "dev-latest"
            },
            "type": "library",
            "extra": {
                "captainhook": {
                    "force-install": true
                },
                "ramsey/conventional-commits": {
                    "configFile": "conventional-commits.json"
                }
            },
            "autoload": {
                "psr-4": {
                    "Ramsey\\Collection\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Ben Ramsey",
                    "email": "<EMAIL>",
                    "homepage": "https://benramsey.com"
                }
            ],
            "description": "A PHP library for representing and manipulating collections.",
            "keywords": [
                "array",
                "collection",
                "hash",
                "map",
                "queue",
                "set"
            ],
            "support": {
                "issues": "https://github.com/ramsey/collection/issues",
                "source": "https://github.com/ramsey/collection/tree/2.1.0"
            },
            "time": "2025-03-02T04:48:29+00:00"
        },
        {
            "name": "ramsey/uuid",
            "version": "4.7.6",
            "source": {
                "type": "git",
                "url": "https://github.com/ramsey/uuid.git",
                "reference": "91039bc1faa45ba123c4328958e620d382ec7088"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/ramsey/uuid/zipball/91039bc1faa45ba123c4328958e620d382ec7088",
                "reference": "91039bc1faa45ba123c4328958e620d382ec7088",
                "shasum": ""
            },
            "require": {
                "brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12",
                "ext-json": "*",
                "php": "^8.0",
                "ramsey/collection": "^1.2 || ^2.0"
            },
            "replace": {
                "rhumsaa/uuid": "self.version"
            },
            "require-dev": {
                "captainhook/captainhook": "^5.10",
                "captainhook/plugin-composer": "^5.3",
                "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0",
                "doctrine/annotations": "^1.8",
                "ergebnis/composer-normalize": "^2.15",
                "mockery/mockery": "^1.3",
                "paragonie/random-lib": "^2",
                "php-mock/php-mock": "^2.2",
                "php-mock/php-mock-mockery": "^1.3",
                "php-parallel-lint/php-parallel-lint": "^1.1",
                "phpbench/phpbench": "^1.0",
                "phpstan/extension-installer": "^1.1",
                "phpstan/phpstan": "^1.8",
                "phpstan/phpstan-mockery": "^1.1",
                "phpstan/phpstan-phpunit": "^1.1",
                "phpunit/phpunit": "^8.5 || ^9",
                "ramsey/composer-repl": "^1.4",
                "slevomat/coding-standard": "^8.4",
                "squizlabs/php_codesniffer": "^3.5",
                "vimeo/psalm": "^4.9"
            },
            "suggest": {
                "ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.",
                "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.",
                "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.",
                "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter",
                "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."
            },
            "type": "library",
            "extra": {
                "captainhook": {
                    "force-install": true
                }
            },
            "autoload": {
                "files": [
                    "src/functions.php"
                ],
                "psr-4": {
                    "Ramsey\\Uuid\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).",
            "keywords": [
                "guid",
                "identifier",
                "uuid"
            ],
            "support": {
                "issues": "https://github.com/ramsey/uuid/issues",
                "source": "https://github.com/ramsey/uuid/tree/4.7.6"
            },
            "funding": [
                {
                    "url": "https://github.com/ramsey",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/ramsey/uuid",
                    "type": "tidelift"
                }
            ],
            "time": "2024-04-27T21:32:50+00:00"
        },
        {
<<<<<<< HEAD
            "name": "socialiteproviders/apple",
            "version": "5.6.1",
            "source": {
                "type": "git",
                "url": "https://github.com/SocialiteProviders/Apple.git",
                "reference": "e00ff7c06e4df297aaeace4e454b2054d6bebe95"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/SocialiteProviders/Apple/zipball/e00ff7c06e4df297aaeace4e454b2054d6bebe95",
                "reference": "e00ff7c06e4df297aaeace4e454b2054d6bebe95",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "ext-openssl": "*",
                "firebase/php-jwt": "^6.8",
                "lcobucci/clock": "^2.0 || ^3.0",
                "lcobucci/jwt": "^4.1.5 || ^5.0.0",
                "php": "^8.0",
                "socialiteproviders/manager": "^4.4"
            },
            "suggest": {
                "ahilmurugesan/socialite-apple-helper": "Automatic Apple client key generation and management."
=======
            "name": "shalvah/clara",
            "version": "3.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/shalvah/clara.git",
                "reference": "cdbb5737cbdd101756d97dd2279a979a1af7710b"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/shalvah/clara/zipball/cdbb5737cbdd101756d97dd2279a979a1af7710b",
                "reference": "cdbb5737cbdd101756d97dd2279a979a1af7710b",
                "shasum": ""
            },
            "require": {
                "php": ">=7.4",
                "symfony/console": "^4.0|^5.0|^6.0|^7.0"
            },
            "require-dev": {
                "eloquent/phony-phpunit": "^7.0",
                "phpunit/phpunit": "^9.1"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "helpers.php"
                ],
                "psr-4": {
                    "Shalvah\\Clara\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "description": "🔊 Simple, pretty, testable console output for CLI apps.",
            "keywords": [
                "cli",
                "log",
                "logging"
            ],
            "support": {
                "issues": "https://github.com/shalvah/clara/issues",
                "source": "https://github.com/shalvah/clara/tree/3.2.0"
            },
            "time": "2024-02-27T20:30:59+00:00"
        },
        {
            "name": "shalvah/upgrader",
            "version": "0.6.0",
            "source": {
                "type": "git",
                "url": "https://github.com/shalvah/upgrader.git",
                "reference": "d95ed17fe9f5e1ee7d47ad835595f1af080a867f"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/shalvah/upgrader/zipball/d95ed17fe9f5e1ee7d47ad835595f1af080a867f",
                "reference": "d95ed17fe9f5e1ee7d47ad835595f1af080a867f",
                "shasum": ""
            },
            "require": {
                "illuminate/support": ">=8.0",
                "nikic/php-parser": "^5.0",
                "php": ">=8.0"
            },
            "require-dev": {
                "dms/phpunit-arraysubset-asserts": "^0.2.0",
                "pestphp/pest": "^1.21",
                "phpstan/phpstan": "^1.0",
                "spatie/ray": "^1.33"
>>>>>>> 8d3461b4ca66c19e1163108f7782a88cb5e139b2
            },
            "type": "library",
            "autoload": {
                "psr-4": {
<<<<<<< HEAD
                    "SocialiteProviders\\Apple\\": ""
=======
                    "Shalvah\\Upgrader\\": "src/"
>>>>>>> 8d3461b4ca66c19e1163108f7782a88cb5e139b2
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
<<<<<<< HEAD
                    "name": "Ahilan",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Vamsi Krishna V",
                    "email": "<EMAIL>",
                    "homepage": "https://vonectech.com/",
                    "role": "Farmer"
                }
            ],
            "description": "Apple OAuth2 Provider for Laravel Socialite",
            "keywords": [
                "apple",
                "apple client key",
                "apple sign in",
                "client key generator",
                "client key refresh",
                "laravel",
                "laravel apple",
                "laravel socialite",
                "oauth",
                "provider",
                "sign in with apple",
                "socialite",
                "socialite apple"
            ],
            "support": {
                "docs": "https://socialiteproviders.com/apple",
                "issues": "https://github.com/socialiteproviders/providers/issues",
                "source": "https://github.com/socialiteproviders/providers"
            },
            "time": "2023-12-06T14:43:17+00:00"
=======
                    "name": "Shalvah",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Create automatic upgrades for your package.",
            "homepage": "http://github.com/shalvah/upgrader",
            "keywords": [
                "upgrade"
            ],
            "support": {
                "issues": "https://github.com/shalvah/upgrader/issues",
                "source": "https://github.com/shalvah/upgrader/tree/0.6.0"
            },
            "funding": [
                {
                    "url": "https://patreon.com/shalvah",
                    "type": "patreon"
                }
            ],
            "time": "2024-02-20T11:51:46+00:00"
>>>>>>> 8d3461b4ca66c19e1163108f7782a88cb5e139b2
        },
        {
            "name": "socialiteproviders/google",
            "version": "4.1.0",
            "source": {
                "type": "git",
                "url": "https://github.com/SocialiteProviders/Google-Plus.git",
                "reference": "1cb8f6fb2c0dd0fc8b34e95f69865663fdf0b401"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/SocialiteProviders/Google-Plus/zipball/1cb8f6fb2c0dd0fc8b34e95f69865663fdf0b401",
                "reference": "1cb8f6fb2c0dd0fc8b34e95f69865663fdf0b401",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "php": "^7.2 || ^8.0",
                "socialiteproviders/manager": "~4.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "SocialiteProviders\\Google\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "xstoop",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Google OAuth2 Provider for Laravel Socialite",
            "support": {
                "source": "https://github.com/SocialiteProviders/Google-Plus/tree/4.1.0"
            },
            "time": "2020-12-01T23:10:59+00:00"
        },
        {
            "name": "socialiteproviders/manager",
            "version": "v4.8.1",
            "source": {
                "type": "git",
                "url": "https://github.com/SocialiteProviders/Manager.git",
                "reference": "8180ec14bef230ec2351cff993d5d2d7ca470ef4"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/SocialiteProviders/Manager/zipball/8180ec14bef230ec2351cff993d5d2d7ca470ef4",
                "reference": "8180ec14bef230ec2351cff993d5d2d7ca470ef4",
                "shasum": ""
            },
            "require": {
                "illuminate/support": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0",
                "laravel/socialite": "^5.5",
                "php": "^8.1"
            },
            "require-dev": {
                "mockery/mockery": "^1.2",
                "phpunit/phpunit": "^9.0"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "SocialiteProviders\\Manager\\ServiceProvider"
                    ]
                }
            },
            "autoload": {
                "psr-4": {
                    "SocialiteProviders\\Manager\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Andy Wendt",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Anton Komarev",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Miguel Piedrafita",
                    "email": "<EMAIL>"
                },
                {
                    "name": "atymic",
                    "email": "<EMAIL>",
                    "homepage": "https://atymic.dev"
                }
            ],
            "description": "Easily add new or override built-in providers in Laravel Socialite.",
            "homepage": "https://socialiteproviders.com",
            "keywords": [
                "laravel",
                "manager",
                "oauth",
                "providers",
                "socialite"
            ],
            "support": {
                "issues": "https://github.com/socialiteproviders/manager/issues",
                "source": "https://github.com/socialiteproviders/manager"
            },
            "time": "2025-02-24T19:33:30+00:00"
        },
        {
            "name": "spatie/data-transfer-object",
            "version": "3.9.1",
            "source": {
                "type": "git",
                "url": "https://github.com/spatie/data-transfer-object.git",
                "reference": "1df0906c4e9e3aebd6c0506fd82c8b7d5548c1c8"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/spatie/data-transfer-object/zipball/1df0906c4e9e3aebd6c0506fd82c8b7d5548c1c8",
                "reference": "1df0906c4e9e3aebd6c0506fd82c8b7d5548c1c8",
                "shasum": ""
            },
            "require": {
                "php": "^8.0"
            },
            "require-dev": {
                "illuminate/collections": "^8.36",
                "jetbrains/phpstorm-attributes": "^1.0",
                "larapack/dd": "^1.1",
                "phpunit/phpunit": "^9.5.5"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Spatie\\DataTransferObject\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Brent Roose",
                    "email": "<EMAIL>",
                    "homepage": "https://spatie.be",
                    "role": "Developer"
                }
            ],
            "description": "Data transfer objects with batteries included",
            "homepage": "https://github.com/spatie/data-transfer-object",
            "keywords": [
                "data-transfer-object",
                "spatie"
            ],
            "support": {
                "issues": "https://github.com/spatie/data-transfer-object/issues",
                "source": "https://github.com/spatie/data-transfer-object/tree/3.9.1"
            },
            "funding": [
                {
                    "url": "https://spatie.be/open-source/support-us",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/spatie",
                    "type": "github"
                }
            ],
            "abandoned": "spatie/laravel-data",
            "time": "2022-09-16T13:34:38+00:00"
        },
        {
            "name": "spatie/laravel-permission",
            "version": "6.16.0",
            "source": {
                "type": "git",
                "url": "https://github.com/spatie/laravel-permission.git",
                "reference": "4fa03c06509e037a4d42c131d0f181e3e4bbd483"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/spatie/laravel-permission/zipball/4fa03c06509e037a4d42c131d0f181e3e4bbd483",
                "reference": "4fa03c06509e037a4d42c131d0f181e3e4bbd483",
                "shasum": ""
            },
            "require": {
                "illuminate/auth": "^8.12|^9.0|^10.0|^11.0|^12.0",
                "illuminate/container": "^8.12|^9.0|^10.0|^11.0|^12.0",
                "illuminate/contracts": "^8.12|^9.0|^10.0|^11.0|^12.0",
                "illuminate/database": "^8.12|^9.0|^10.0|^11.0|^12.0",
                "php": "^8.0"
            },
            "require-dev": {
                "laravel/passport": "^11.0|^12.0",
                "laravel/pint": "^1.0",
                "orchestra/testbench": "^6.23|^7.0|^8.0|^9.0|^10.0",
                "phpunit/phpunit": "^9.4|^10.1|^11.5"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Spatie\\Permission\\PermissionServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-main": "6.x-dev",
                    "dev-master": "6.x-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/helpers.php"
                ],
                "psr-4": {
                    "Spatie\\Permission\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Freek Van der Herten",
                    "email": "<EMAIL>",
                    "homepage": "https://spatie.be",
                    "role": "Developer"
                }
            ],
            "description": "Permission handling for Laravel 8.0 and up",
            "homepage": "https://github.com/spatie/laravel-permission",
            "keywords": [
                "acl",
                "laravel",
                "permission",
                "permissions",
                "rbac",
                "roles",
                "security",
                "spatie"
            ],
            "support": {
                "issues": "https://github.com/spatie/laravel-permission/issues",
                "source": "https://github.com/spatie/laravel-permission/tree/6.16.0"
            },
            "funding": [
                {
                    "url": "https://github.com/spatie",
                    "type": "github"
                }
            ],
            "time": "2025-02-28T20:29:57+00:00"
        },
        {
            "name": "symfony/clock",
            "version": "v7.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/clock.git",
                "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24",
                "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "psr/clock": "^1.0",
                "symfony/polyfill-php83": "^1.28"
            },
            "provide": {
                "psr/clock-implementation": "1.0"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "Resources/now.php"
                ],
                "psr-4": {
                    "Symfony\\Component\\Clock\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Decouples applications from the system clock",
            "homepage": "https://symfony.com",
            "keywords": [
                "clock",
                "psr20",
                "time"
            ],
            "support": {
                "source": "https://github.com/symfony/clock/tree/v7.2.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:21:43+00:00"
        },
        {
            "name": "symfony/console",
            "version": "v7.2.1",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/console.git",
                "reference": "fefcc18c0f5d0efe3ab3152f15857298868dc2c3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/console/zipball/fefcc18c0f5d0efe3ab3152f15857298868dc2c3",
                "reference": "fefcc18c0f5d0efe3ab3152f15857298868dc2c3",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/polyfill-mbstring": "~1.0",
                "symfony/service-contracts": "^2.5|^3",
                "symfony/string": "^6.4|^7.0"
            },
            "conflict": {
                "symfony/dependency-injection": "<6.4",
                "symfony/dotenv": "<6.4",
                "symfony/event-dispatcher": "<6.4",
                "symfony/lock": "<6.4",
                "symfony/process": "<6.4"
            },
            "provide": {
                "psr/log-implementation": "1.0|2.0|3.0"
            },
            "require-dev": {
                "psr/log": "^1|^2|^3",
                "symfony/config": "^6.4|^7.0",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/event-dispatcher": "^6.4|^7.0",
                "symfony/http-foundation": "^6.4|^7.0",
                "symfony/http-kernel": "^6.4|^7.0",
                "symfony/lock": "^6.4|^7.0",
                "symfony/messenger": "^6.4|^7.0",
                "symfony/process": "^6.4|^7.0",
                "symfony/stopwatch": "^6.4|^7.0",
                "symfony/var-dumper": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Console\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Eases the creation of beautiful and testable command line interfaces",
            "homepage": "https://symfony.com",
            "keywords": [
                "cli",
                "command-line",
                "console",
                "terminal"
            ],
            "support": {
                "source": "https://github.com/symfony/console/tree/v7.2.1"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-12-11T03:49:26+00:00"
        },
        {
            "name": "symfony/css-selector",
            "version": "v7.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/css-selector.git",
                "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2",
                "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\CssSelector\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jean-François Simon",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Converts CSS selectors to XPath expressions",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/css-selector/tree/v7.2.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:21:43+00:00"
        },
        {
            "name": "symfony/deprecation-contracts",
            "version": "v3.5.1",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/deprecation-contracts.git",
                "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6",
                "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6",
                "shasum": ""
            },
            "require": {
                "php": ">=8.1"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/contracts",
                    "name": "symfony/contracts"
                },
                "branch-alias": {
                    "dev-main": "3.5-dev"
                }
            },
            "autoload": {
                "files": [
                    "function.php"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "A generic function and convention to trigger deprecation notices",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:20:29+00:00"
        },
        {
            "name": "symfony/error-handler",
            "version": "v7.2.4",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/error-handler.git",
                "reference": "aabf79938aa795350c07ce6464dd1985607d95d5"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/error-handler/zipball/aabf79938aa795350c07ce6464dd1985607d95d5",
                "reference": "aabf79938aa795350c07ce6464dd1985607d95d5",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "psr/log": "^1|^2|^3",
                "symfony/var-dumper": "^6.4|^7.0"
            },
            "conflict": {
                "symfony/deprecation-contracts": "<2.5",
                "symfony/http-kernel": "<6.4"
            },
            "require-dev": {
                "symfony/deprecation-contracts": "^2.5|^3",
                "symfony/http-kernel": "^6.4|^7.0",
                "symfony/serializer": "^6.4|^7.0"
            },
            "bin": [
                "Resources/bin/patch-type-declarations"
            ],
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\ErrorHandler\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides tools to manage errors and ease debugging PHP code",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/error-handler/tree/v7.2.4"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-02T20:27:07+00:00"
        },
        {
            "name": "symfony/event-dispatcher",
            "version": "v7.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/event-dispatcher.git",
                "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1",
                "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/event-dispatcher-contracts": "^2.5|^3"
            },
            "conflict": {
                "symfony/dependency-injection": "<6.4",
                "symfony/service-contracts": "<2.5"
            },
            "provide": {
                "psr/event-dispatcher-implementation": "1.0",
                "symfony/event-dispatcher-implementation": "2.0|3.0"
            },
            "require-dev": {
                "psr/log": "^1|^2|^3",
                "symfony/config": "^6.4|^7.0",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/error-handler": "^6.4|^7.0",
                "symfony/expression-language": "^6.4|^7.0",
                "symfony/http-foundation": "^6.4|^7.0",
                "symfony/service-contracts": "^2.5|^3",
                "symfony/stopwatch": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\EventDispatcher\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:21:43+00:00"
        },
        {
            "name": "symfony/event-dispatcher-contracts",
            "version": "v3.5.1",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/event-dispatcher-contracts.git",
                "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f",
                "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f",
                "shasum": ""
            },
            "require": {
                "php": ">=8.1",
                "psr/event-dispatcher": "^1"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/contracts",
                    "name": "symfony/contracts"
                },
                "branch-alias": {
                    "dev-main": "3.5-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Symfony\\Contracts\\EventDispatcher\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Generic abstractions related to dispatching event",
            "homepage": "https://symfony.com",
            "keywords": [
                "abstractions",
                "contracts",
                "decoupling",
                "interfaces",
                "interoperability",
                "standards"
            ],
            "support": {
                "source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:20:29+00:00"
        },
        {
            "name": "symfony/finder",
            "version": "v7.2.2",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/finder.git",
                "reference": "87a71856f2f56e4100373e92529eed3171695cfb"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb",
                "reference": "87a71856f2f56e4100373e92529eed3171695cfb",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "symfony/filesystem": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Finder\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Finds files and directories via an intuitive fluent interface",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/finder/tree/v7.2.2"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-12-30T19:00:17+00:00"
        },
        {
            "name": "symfony/http-foundation",
            "version": "v7.2.3",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/http-foundation.git",
                "reference": "ee1b504b8926198be89d05e5b6fc4c3810c090f0"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/http-foundation/zipball/ee1b504b8926198be89d05e5b6fc4c3810c090f0",
                "reference": "ee1b504b8926198be89d05e5b6fc4c3810c090f0",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/deprecation-contracts": "^2.5|^3.0",
                "symfony/polyfill-mbstring": "~1.1",
                "symfony/polyfill-php83": "^1.27"
            },
            "conflict": {
                "doctrine/dbal": "<3.6",
                "symfony/cache": "<6.4.12|>=7.0,<7.1.5"
            },
            "require-dev": {
                "doctrine/dbal": "^3.6|^4",
                "predis/predis": "^1.1|^2.0",
                "symfony/cache": "^6.4.12|^7.1.5",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/expression-language": "^6.4|^7.0",
                "symfony/http-kernel": "^6.4|^7.0",
                "symfony/mime": "^6.4|^7.0",
                "symfony/rate-limiter": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\HttpFoundation\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Defines an object-oriented layer for the HTTP specification",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/http-foundation/tree/v7.2.3"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-01-17T10:56:55+00:00"
        },
        {
            "name": "symfony/http-kernel",
            "version": "v7.2.4",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/http-kernel.git",
                "reference": "9f1103734c5789798fefb90e91de4586039003ed"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/http-kernel/zipball/9f1103734c5789798fefb90e91de4586039003ed",
                "reference": "9f1103734c5789798fefb90e91de4586039003ed",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "psr/log": "^1|^2|^3",
                "symfony/deprecation-contracts": "^2.5|^3",
                "symfony/error-handler": "^6.4|^7.0",
                "symfony/event-dispatcher": "^6.4|^7.0",
                "symfony/http-foundation": "^6.4|^7.0",
                "symfony/polyfill-ctype": "^1.8"
            },
            "conflict": {
                "symfony/browser-kit": "<6.4",
                "symfony/cache": "<6.4",
                "symfony/config": "<6.4",
                "symfony/console": "<6.4",
                "symfony/dependency-injection": "<6.4",
                "symfony/doctrine-bridge": "<6.4",
                "symfony/form": "<6.4",
                "symfony/http-client": "<6.4",
                "symfony/http-client-contracts": "<2.5",
                "symfony/mailer": "<6.4",
                "symfony/messenger": "<6.4",
                "symfony/translation": "<6.4",
                "symfony/translation-contracts": "<2.5",
                "symfony/twig-bridge": "<6.4",
                "symfony/validator": "<6.4",
                "symfony/var-dumper": "<6.4",
                "twig/twig": "<3.12"
            },
            "provide": {
                "psr/log-implementation": "1.0|2.0|3.0"
            },
            "require-dev": {
                "psr/cache": "^1.0|^2.0|^3.0",
                "symfony/browser-kit": "^6.4|^7.0",
                "symfony/clock": "^6.4|^7.0",
                "symfony/config": "^6.4|^7.0",
                "symfony/console": "^6.4|^7.0",
                "symfony/css-selector": "^6.4|^7.0",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/dom-crawler": "^6.4|^7.0",
                "symfony/expression-language": "^6.4|^7.0",
                "symfony/finder": "^6.4|^7.0",
                "symfony/http-client-contracts": "^2.5|^3",
                "symfony/process": "^6.4|^7.0",
                "symfony/property-access": "^7.1",
                "symfony/routing": "^6.4|^7.0",
                "symfony/serializer": "^7.1",
                "symfony/stopwatch": "^6.4|^7.0",
                "symfony/translation": "^6.4|^7.0",
                "symfony/translation-contracts": "^2.5|^3",
                "symfony/uid": "^6.4|^7.0",
                "symfony/validator": "^6.4|^7.0",
                "symfony/var-dumper": "^6.4|^7.0",
                "symfony/var-exporter": "^6.4|^7.0",
                "twig/twig": "^3.12"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\HttpKernel\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides a structured process for converting a Request into a Response",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/http-kernel/tree/v7.2.4"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-26T11:01:22+00:00"
        },
        {
            "name": "symfony/mailer",
            "version": "v7.2.3",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/mailer.git",
                "reference": "f3871b182c44997cf039f3b462af4a48fb85f9d3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/mailer/zipball/f3871b182c44997cf039f3b462af4a48fb85f9d3",
                "reference": "f3871b182c44997cf039f3b462af4a48fb85f9d3",
                "shasum": ""
            },
            "require": {
                "egulias/email-validator": "^2.1.10|^3|^4",
                "php": ">=8.2",
                "psr/event-dispatcher": "^1",
                "psr/log": "^1|^2|^3",
                "symfony/event-dispatcher": "^6.4|^7.0",
                "symfony/mime": "^7.2",
                "symfony/service-contracts": "^2.5|^3"
            },
            "conflict": {
                "symfony/http-client-contracts": "<2.5",
                "symfony/http-kernel": "<6.4",
                "symfony/messenger": "<6.4",
                "symfony/mime": "<6.4",
                "symfony/twig-bridge": "<6.4"
            },
            "require-dev": {
                "symfony/console": "^6.4|^7.0",
                "symfony/http-client": "^6.4|^7.0",
                "symfony/messenger": "^6.4|^7.0",
                "symfony/twig-bridge": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Mailer\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Helps sending emails",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/mailer/tree/v7.2.3"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-01-27T11:08:17+00:00"
        },
        {
            "name": "symfony/mime",
            "version": "v7.2.4",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/mime.git",
                "reference": "87ca22046b78c3feaff04b337f33b38510fd686b"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/mime/zipball/87ca22046b78c3feaff04b337f33b38510fd686b",
                "reference": "87ca22046b78c3feaff04b337f33b38510fd686b",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/polyfill-intl-idn": "^1.10",
                "symfony/polyfill-mbstring": "^1.0"
            },
            "conflict": {
                "egulias/email-validator": "~3.0.0",
                "phpdocumentor/reflection-docblock": "<3.2.2",
                "phpdocumentor/type-resolver": "<1.4.0",
                "symfony/mailer": "<6.4",
                "symfony/serializer": "<6.4.3|>7.0,<7.0.3"
            },
            "require-dev": {
                "egulias/email-validator": "^2.1.10|^3.1|^4",
                "league/html-to-markdown": "^5.0",
                "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/process": "^6.4|^7.0",
                "symfony/property-access": "^6.4|^7.0",
                "symfony/property-info": "^6.4|^7.0",
                "symfony/serializer": "^6.4.3|^7.0.3"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Mime\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Allows manipulating MIME messages",
            "homepage": "https://symfony.com",
            "keywords": [
                "mime",
                "mime-type"
            ],
            "support": {
                "source": "https://github.com/symfony/mime/tree/v7.2.4"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-19T08:51:20+00:00"
        },
        {
            "name": "symfony/polyfill-ctype",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-ctype.git",
                "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638",
                "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "provide": {
                "ext-ctype": "*"
            },
            "suggest": {
                "ext-ctype": "For best performance"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Ctype\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Gert de Pagter",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill for ctype functions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "ctype",
                "polyfill",
                "portable"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-intl-grapheme",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-intl-grapheme.git",
                "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe",
                "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "suggest": {
                "ext-intl": "For best performance"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Intl\\Grapheme\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill for intl's grapheme_* functions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "grapheme",
                "intl",
                "polyfill",
                "portable",
                "shim"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-intl-idn",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-intl-idn.git",
                "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773",
                "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2",
                "symfony/polyfill-intl-normalizer": "^1.10"
            },
            "suggest": {
                "ext-intl": "For best performance"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Intl\\Idn\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Laurent Bassin",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Trevor Rowbotham",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "idn",
                "intl",
                "polyfill",
                "portable",
                "shim"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-intl-normalizer",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-intl-normalizer.git",
                "reference": "3833d7255cc303546435cb650316bff708a1c75c"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c",
                "reference": "3833d7255cc303546435cb650316bff708a1c75c",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "suggest": {
                "ext-intl": "For best performance"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Intl\\Normalizer\\": ""
                },
                "classmap": [
                    "Resources/stubs"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill for intl's Normalizer class and related functions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "intl",
                "normalizer",
                "polyfill",
                "portable",
                "shim"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-mbstring",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-mbstring.git",
                "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341",
                "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "provide": {
                "ext-mbstring": "*"
            },
            "suggest": {
                "ext-mbstring": "For best performance"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Mbstring\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill for the Mbstring extension",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "mbstring",
                "polyfill",
                "portable",
                "shim"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-php80",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-php80.git",
                "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8",
                "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Php80\\": ""
                },
                "classmap": [
                    "Resources/stubs"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Ion Bazan",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "polyfill",
                "portable",
                "shim"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-php83",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-php83.git",
                "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491",
                "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Php83\\": ""
                },
                "classmap": [
                    "Resources/stubs"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "polyfill",
                "portable",
                "shim"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/polyfill-uuid",
            "version": "v1.31.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/polyfill-uuid.git",
                "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2",
                "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2",
                "shasum": ""
            },
            "require": {
                "php": ">=7.2"
            },
            "provide": {
                "ext-uuid": "*"
            },
            "suggest": {
                "ext-uuid": "For best performance"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/polyfill",
                    "name": "symfony/polyfill"
                }
            },
            "autoload": {
                "files": [
                    "bootstrap.php"
                ],
                "psr-4": {
                    "Symfony\\Polyfill\\Uuid\\": ""
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Grégoire Pineau",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Symfony polyfill for uuid functions",
            "homepage": "https://symfony.com",
            "keywords": [
                "compatibility",
                "polyfill",
                "portable",
                "uuid"
            ],
            "support": {
                "source": "https://github.com/symfony/polyfill-uuid/tree/v1.31.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-09T11:45:10+00:00"
        },
        {
            "name": "symfony/process",
            "version": "v7.2.4",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/process.git",
                "reference": "d8f411ff3c7ddc4ae9166fb388d1190a2df5b5cf"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/process/zipball/d8f411ff3c7ddc4ae9166fb388d1190a2df5b5cf",
                "reference": "d8f411ff3c7ddc4ae9166fb388d1190a2df5b5cf",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Process\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Executes commands in sub-processes",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/process/tree/v7.2.4"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-05T08:33:46+00:00"
        },
        {
            "name": "symfony/routing",
            "version": "v7.2.3",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/routing.git",
                "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/routing/zipball/ee9a67edc6baa33e5fae662f94f91fd262930996",
                "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/deprecation-contracts": "^2.5|^3"
            },
            "conflict": {
                "symfony/config": "<6.4",
                "symfony/dependency-injection": "<6.4",
                "symfony/yaml": "<6.4"
            },
            "require-dev": {
                "psr/log": "^1|^2|^3",
                "symfony/config": "^6.4|^7.0",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/expression-language": "^6.4|^7.0",
                "symfony/http-foundation": "^6.4|^7.0",
                "symfony/yaml": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Routing\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Maps an HTTP request to a set of configuration variables",
            "homepage": "https://symfony.com",
            "keywords": [
                "router",
                "routing",
                "uri",
                "url"
            ],
            "support": {
                "source": "https://github.com/symfony/routing/tree/v7.2.3"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-01-17T10:56:55+00:00"
        },
        {
            "name": "symfony/service-contracts",
            "version": "v3.5.1",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/service-contracts.git",
                "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0",
                "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0",
                "shasum": ""
            },
            "require": {
                "php": ">=8.1",
                "psr/container": "^1.1|^2.0",
                "symfony/deprecation-contracts": "^2.5|^3"
            },
            "conflict": {
                "ext-psr": "<1.1|>=2"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/contracts",
                    "name": "symfony/contracts"
                },
                "branch-alias": {
                    "dev-main": "3.5-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Symfony\\Contracts\\Service\\": ""
                },
                "exclude-from-classmap": [
                    "/Test/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Generic abstractions related to writing services",
            "homepage": "https://symfony.com",
            "keywords": [
                "abstractions",
                "contracts",
                "decoupling",
                "interfaces",
                "interoperability",
                "standards"
            ],
            "support": {
                "source": "https://github.com/symfony/service-contracts/tree/v3.5.1"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:20:29+00:00"
        },
        {
            "name": "symfony/string",
            "version": "v7.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/string.git",
                "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/string/zipball/446e0d146f991dde3e73f45f2c97a9faad773c82",
                "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/polyfill-ctype": "~1.8",
                "symfony/polyfill-intl-grapheme": "~1.0",
                "symfony/polyfill-intl-normalizer": "~1.0",
                "symfony/polyfill-mbstring": "~1.0"
            },
            "conflict": {
                "symfony/translation-contracts": "<2.5"
            },
            "require-dev": {
                "symfony/emoji": "^7.1",
                "symfony/error-handler": "^6.4|^7.0",
                "symfony/http-client": "^6.4|^7.0",
                "symfony/intl": "^6.4|^7.0",
                "symfony/translation-contracts": "^2.5|^3.0",
                "symfony/var-exporter": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "Resources/functions.php"
                ],
                "psr-4": {
                    "Symfony\\Component\\String\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way",
            "homepage": "https://symfony.com",
            "keywords": [
                "grapheme",
                "i18n",
                "string",
                "unicode",
                "utf-8",
                "utf8"
            ],
            "support": {
                "source": "https://github.com/symfony/string/tree/v7.2.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-11-13T13:31:26+00:00"
        },
        {
            "name": "symfony/translation",
            "version": "v7.2.4",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/translation.git",
                "reference": "283856e6981286cc0d800b53bd5703e8e363f05a"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/translation/zipball/283856e6981286cc0d800b53bd5703e8e363f05a",
                "reference": "283856e6981286cc0d800b53bd5703e8e363f05a",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/deprecation-contracts": "^2.5|^3",
                "symfony/polyfill-mbstring": "~1.0",
                "symfony/translation-contracts": "^2.5|^3.0"
            },
            "conflict": {
                "symfony/config": "<6.4",
                "symfony/console": "<6.4",
                "symfony/dependency-injection": "<6.4",
                "symfony/http-client-contracts": "<2.5",
                "symfony/http-kernel": "<6.4",
                "symfony/service-contracts": "<2.5",
                "symfony/twig-bundle": "<6.4",
                "symfony/yaml": "<6.4"
            },
            "provide": {
                "symfony/translation-implementation": "2.3|3.0"
            },
            "require-dev": {
                "nikic/php-parser": "^4.18|^5.0",
                "psr/log": "^1|^2|^3",
                "symfony/config": "^6.4|^7.0",
                "symfony/console": "^6.4|^7.0",
                "symfony/dependency-injection": "^6.4|^7.0",
                "symfony/finder": "^6.4|^7.0",
                "symfony/http-client-contracts": "^2.5|^3.0",
                "symfony/http-kernel": "^6.4|^7.0",
                "symfony/intl": "^6.4|^7.0",
                "symfony/polyfill-intl-icu": "^1.21",
                "symfony/routing": "^6.4|^7.0",
                "symfony/service-contracts": "^2.5|^3",
                "symfony/yaml": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "Resources/functions.php"
                ],
                "psr-4": {
                    "Symfony\\Component\\Translation\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides tools to internationalize your application",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/translation/tree/v7.2.4"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-13T10:27:23+00:00"
        },
        {
            "name": "symfony/translation-contracts",
            "version": "v3.5.1",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/translation-contracts.git",
                "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c",
                "reference": "4667ff3bd513750603a09c8dedbea942487fb07c",
                "shasum": ""
            },
            "require": {
                "php": ">=8.1"
            },
            "type": "library",
            "extra": {
                "thanks": {
                    "url": "https://github.com/symfony/contracts",
                    "name": "symfony/contracts"
                },
                "branch-alias": {
                    "dev-main": "3.5-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Symfony\\Contracts\\Translation\\": ""
                },
                "exclude-from-classmap": [
                    "/Test/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Generic abstractions related to translation",
            "homepage": "https://symfony.com",
            "keywords": [
                "abstractions",
                "contracts",
                "decoupling",
                "interfaces",
                "interoperability",
                "standards"
            ],
            "support": {
                "source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:20:29+00:00"
        },
        {
            "name": "symfony/uid",
            "version": "v7.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/uid.git",
                "reference": "2d294d0c48df244c71c105a169d0190bfb080426"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/uid/zipball/2d294d0c48df244c71c105a169d0190bfb080426",
                "reference": "2d294d0c48df244c71c105a169d0190bfb080426",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/polyfill-uuid": "^1.15"
            },
            "require-dev": {
                "symfony/console": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Uid\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Grégoire Pineau",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides an object-oriented API to generate and represent UIDs",
            "homepage": "https://symfony.com",
            "keywords": [
                "UID",
                "ulid",
                "uuid"
            ],
            "support": {
                "source": "https://github.com/symfony/uid/tree/v7.2.0"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2024-09-25T14:21:43+00:00"
        },
        {
            "name": "symfony/var-dumper",
            "version": "v7.2.3",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/var-dumper.git",
                "reference": "82b478c69745d8878eb60f9a049a4d584996f73a"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/var-dumper/zipball/82b478c69745d8878eb60f9a049a4d584996f73a",
                "reference": "82b478c69745d8878eb60f9a049a4d584996f73a",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/polyfill-mbstring": "~1.0"
            },
            "conflict": {
                "symfony/console": "<6.4"
            },
            "require-dev": {
                "ext-iconv": "*",
                "symfony/console": "^6.4|^7.0",
                "symfony/http-kernel": "^6.4|^7.0",
                "symfony/process": "^6.4|^7.0",
                "symfony/uid": "^6.4|^7.0",
                "twig/twig": "^3.12"
            },
            "bin": [
                "Resources/bin/var-dump-server"
            ],
            "type": "library",
            "autoload": {
                "files": [
                    "Resources/functions/dump.php"
                ],
                "psr-4": {
                    "Symfony\\Component\\VarDumper\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Provides mechanisms for walking through any arbitrary PHP variable",
            "homepage": "https://symfony.com",
            "keywords": [
                "debug",
                "dump"
            ],
            "support": {
                "source": "https://github.com/symfony/var-dumper/tree/v7.2.3"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-01-17T11:39:41+00:00"
        },
        {
            "name": "symfony/var-exporter",
            "version": "v7.2.4",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/var-exporter.git",
                "reference": "4ede73aa7a73d81506002d2caadbbdad1ef5b69a"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/var-exporter/zipball/4ede73aa7a73d81506002d2caadbbdad1ef5b69a",
                "reference": "4ede73aa7a73d81506002d2caadbbdad1ef5b69a",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "symfony/property-access": "^6.4|^7.0",
                "symfony/serializer": "^6.4|^7.0",
                "symfony/var-dumper": "^6.4|^7.0"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\VarExporter\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nicolas Grekas",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Allows exporting any serializable PHP data structure to plain PHP code",
            "homepage": "https://symfony.com",
            "keywords": [
                "clone",
                "construct",
                "export",
                "hydrate",
                "instantiate",
                "lazy-loading",
                "proxy",
                "serialize"
            ],
            "support": {
                "source": "https://github.com/symfony/var-exporter/tree/v7.2.4"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-13T10:27:23+00:00"
        },
        {
            "name": "symfony/yaml",
            "version": "v7.2.3",
            "source": {
                "type": "git",
                "url": "https://github.com/symfony/yaml.git",
                "reference": "ac238f173df0c9c1120f862d0f599e17535a87ec"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/symfony/yaml/zipball/ac238f173df0c9c1120f862d0f599e17535a87ec",
                "reference": "ac238f173df0c9c1120f862d0f599e17535a87ec",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "symfony/deprecation-contracts": "^2.5|^3.0",
                "symfony/polyfill-ctype": "^1.8"
            },
            "conflict": {
                "symfony/console": "<6.4"
            },
            "require-dev": {
                "symfony/console": "^6.4|^7.0"
            },
            "bin": [
                "Resources/bin/yaml-lint"
            ],
            "type": "library",
            "autoload": {
                "psr-4": {
                    "Symfony\\Component\\Yaml\\": ""
                },
                "exclude-from-classmap": [
                    "/Tests/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Fabien Potencier",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Symfony Community",
                    "homepage": "https://symfony.com/contributors"
                }
            ],
            "description": "Loads and dumps YAML files",
            "homepage": "https://symfony.com",
            "support": {
                "source": "https://github.com/symfony/yaml/tree/v7.2.3"
            },
            "funding": [
                {
                    "url": "https://symfony.com/sponsor",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/fabpot",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/symfony/symfony",
                    "type": "tidelift"
                }
            ],
            "time": "2025-01-07T12:55:42+00:00"
        },
        {
            "name": "tijsverkoyen/css-to-inline-styles",
            "version": "v2.3.0",
            "source": {
                "type": "git",
                "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git",
                "reference": "0d72ac1c00084279c1816675284073c5a337c20d"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0d72ac1c00084279c1816675284073c5a337c20d",
                "reference": "0d72ac1c00084279c1816675284073c5a337c20d",
                "shasum": ""
            },
            "require": {
                "ext-dom": "*",
                "ext-libxml": "*",
                "php": "^7.4 || ^8.0",
                "symfony/css-selector": "^5.4 || ^6.0 || ^7.0"
            },
            "require-dev": {
                "phpstan/phpstan": "^2.0",
                "phpstan/phpstan-phpunit": "^2.0",
                "phpunit/phpunit": "^8.5.21 || ^9.5.10"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "TijsVerkoyen\\CssToInlineStyles\\": "src"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Tijs Verkoyen",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.",
            "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles",
            "support": {
                "issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues",
                "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.3.0"
            },
            "time": "2024-12-21T16:25:41+00:00"
        },
        {
            "name": "torann/geoip",
            "version": "3.0.9",
            "source": {
                "type": "git",
                "url": "https://github.com/Torann/laravel-geoip.git",
                "reference": "1ea60c7e1a1608de3885e8cd76389cfe5c8424de"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/Torann/laravel-geoip/zipball/1ea60c7e1a1608de3885e8cd76389cfe5c8424de",
                "reference": "1ea60c7e1a1608de3885e8cd76389cfe5c8424de",
                "shasum": ""
            },
            "require": {
                "illuminate/cache": "^8.0|^9.0|^10.0|^11.0|^12.0",
                "illuminate/console": "^8.0|^9.0|^10.0|^11.0|^12.0",
                "illuminate/support": "^8.0|^9.0|^10.0|^11.0|^12.0",
                "php": "^8.0|^8.1|^8.2|^8.3"
            },
            "require-dev": {
                "geoip2/geoip2": "~2.1|~3.0",
                "mockery/mockery": "^1.3",
                "phpstan/phpstan": "^0.12.14|^1.9",
                "phpunit/phpunit": "^8.0|^9.0|^10.0|^11.0",
                "squizlabs/php_codesniffer": "^3.5",
                "vlucas/phpdotenv": "^5.0"
            },
            "suggest": {
                "geoip2/geoip2": "Required to use the MaxMind database or web service with GeoIP (~2.1).",
                "monolog/monolog": "Allows for storing location not found errors to the log"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "aliases": {
                        "GeoIP": "Torann\\GeoIP\\Facades\\GeoIP"
                    },
                    "providers": [
                        "Torann\\GeoIP\\GeoIPServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-master": "1.0-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/helpers.php"
                ],
                "psr-4": {
                    "Torann\\GeoIP\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-2-Clause"
            ],
            "authors": [
                {
                    "name": "Daniel Stainback",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Support for multiple Geographical Location services.",
            "keywords": [
                "IP API",
                "geographical",
                "geoip",
                "geolocation",
                "infoDB",
                "laravel",
                "location"
            ],
            "support": {
                "issues": "https://github.com/Torann/laravel-geoip/issues",
                "source": "https://github.com/Torann/laravel-geoip/tree/3.0.9"
            },
            "time": "2025-02-25T18:24:54+00:00"
        },
        {
            "name": "vlucas/phpdotenv",
            "version": "v5.6.1",
            "source": {
                "type": "git",
                "url": "https://github.com/vlucas/phpdotenv.git",
                "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2",
                "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2",
                "shasum": ""
            },
            "require": {
                "ext-pcre": "*",
                "graham-campbell/result-type": "^1.1.3",
                "php": "^7.2.5 || ^8.0",
                "phpoption/phpoption": "^1.9.3",
                "symfony/polyfill-ctype": "^1.24",
                "symfony/polyfill-mbstring": "^1.24",
                "symfony/polyfill-php80": "^1.24"
            },
            "require-dev": {
                "bamarni/composer-bin-plugin": "^1.8.2",
                "ext-filter": "*",
                "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"
            },
            "suggest": {
                "ext-filter": "Required to use the boolean validator."
            },
            "type": "library",
            "extra": {
                "bamarni-bin": {
                    "bin-links": true,
                    "forward-command": false
                },
                "branch-alias": {
                    "dev-master": "5.6-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Dotenv\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Graham Campbell",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/GrahamCampbell"
                },
                {
                    "name": "Vance Lucas",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/vlucas"
                }
            ],
            "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.",
            "keywords": [
                "dotenv",
                "env",
                "environment"
            ],
            "support": {
                "issues": "https://github.com/vlucas/phpdotenv/issues",
                "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.1"
            },
            "funding": [
                {
                    "url": "https://github.com/GrahamCampbell",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv",
                    "type": "tidelift"
                }
            ],
            "time": "2024-07-20T21:52:34+00:00"
        },
        {
            "name": "voku/portable-ascii",
            "version": "2.0.3",
            "source": {
                "type": "git",
                "url": "https://github.com/voku/portable-ascii.git",
                "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b1d923f88091c6bf09699efcd7c8a1b1bfd7351d",
                "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d",
                "shasum": ""
            },
            "require": {
                "php": ">=7.0.0"
            },
            "require-dev": {
                "phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"
            },
            "suggest": {
                "ext-intl": "Use Intl for transliterator_transliterate() support"
            },
            "type": "library",
            "autoload": {
                "psr-4": {
                    "voku\\": "src/voku/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Lars Moelleken",
                    "homepage": "https://www.moelleken.org/"
                }
            ],
            "description": "Portable ASCII library - performance optimized (ascii) string functions for php.",
            "homepage": "https://github.com/voku/portable-ascii",
            "keywords": [
                "ascii",
                "clean",
                "php"
            ],
            "support": {
                "issues": "https://github.com/voku/portable-ascii/issues",
                "source": "https://github.com/voku/portable-ascii/tree/2.0.3"
            },
            "funding": [
                {
                    "url": "https://www.paypal.me/moelleken",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/voku",
                    "type": "github"
                },
                {
                    "url": "https://opencollective.com/portable-ascii",
                    "type": "open_collective"
                },
                {
                    "url": "https://www.patreon.com/voku",
                    "type": "patreon"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii",
                    "type": "tidelift"
                }
            ],
            "time": "2024-11-21T01:49:47+00:00"
        },
        {
            "name": "webmozart/assert",
            "version": "1.11.0",
            "source": {
                "type": "git",
                "url": "https://github.com/webmozarts/assert.git",
                "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991",
                "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991",
                "shasum": ""
            },
            "require": {
                "ext-ctype": "*",
                "php": "^7.2 || ^8.0"
            },
            "conflict": {
                "phpstan/phpstan": "<0.12.20",
                "vimeo/psalm": "<4.6.1 || 4.6.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^8.5.13"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "1.10-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Webmozart\\Assert\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Bernhard Schussek",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Assertions to validate method input/output with nice error messages.",
            "keywords": [
                "assert",
                "check",
                "validate"
            ],
            "support": {
                "issues": "https://github.com/webmozarts/assert/issues",
                "source": "https://github.com/webmozarts/assert/tree/1.11.0"
            },
            "time": "2022-06-03T18:03:27+00:00"
        }
    ],
    "packages-dev": [
        {
            "name": "hamcrest/hamcrest-php",
            "version": "v2.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/hamcrest/hamcrest-php.git",
                "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3",
                "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3",
                "shasum": ""
            },
            "require": {
                "php": "^5.3|^7.0|^8.0"
            },
            "replace": {
                "cordoval/hamcrest-php": "*",
                "davedevelopment/hamcrest-php": "*",
                "kodova/hamcrest-php": "*"
            },
            "require-dev": {
                "phpunit/php-file-iterator": "^1.4 || ^2.0",
                "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.1-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "hamcrest"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "description": "This is the PHP port of Hamcrest Matchers",
            "keywords": [
                "test"
            ],
            "support": {
                "issues": "https://github.com/hamcrest/hamcrest-php/issues",
                "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"
            },
            "time": "2020-07-09T08:09:16+00:00"
        },
        {
            "name": "laravel/pail",
            "version": "v1.2.2",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/pail.git",
                "reference": "f31f4980f52be17c4667f3eafe034e6826787db2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/pail/zipball/f31f4980f52be17c4667f3eafe034e6826787db2",
                "reference": "f31f4980f52be17c4667f3eafe034e6826787db2",
                "shasum": ""
            },
            "require": {
                "ext-mbstring": "*",
                "illuminate/console": "^10.24|^11.0|^12.0",
                "illuminate/contracts": "^10.24|^11.0|^12.0",
                "illuminate/log": "^10.24|^11.0|^12.0",
                "illuminate/process": "^10.24|^11.0|^12.0",
                "illuminate/support": "^10.24|^11.0|^12.0",
                "nunomaduro/termwind": "^1.15|^2.0",
                "php": "^8.2",
                "symfony/console": "^6.0|^7.0"
            },
            "require-dev": {
                "laravel/framework": "^10.24|^11.0|^12.0",
                "laravel/pint": "^1.13",
                "orchestra/testbench-core": "^8.13|^9.0|^10.0",
                "pestphp/pest": "^2.20|^3.0",
                "pestphp/pest-plugin-type-coverage": "^2.3|^3.0",
                "phpstan/phpstan": "^1.10",
                "symfony/var-dumper": "^6.3|^7.0"
            },
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Laravel\\Pail\\PailServiceProvider"
                    ]
                },
                "branch-alias": {
                    "dev-main": "1.x-dev"
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\Pail\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Nuno Maduro",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Easily delve into your Laravel application's log files directly from the command line.",
            "homepage": "https://github.com/laravel/pail",
            "keywords": [
                "laravel",
                "logs",
                "php",
                "tail"
            ],
            "support": {
                "issues": "https://github.com/laravel/pail/issues",
                "source": "https://github.com/laravel/pail"
            },
            "time": "2025-01-28T15:15:15+00:00"
        },
        {
            "name": "laravel/pint",
            "version": "v1.21.1",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/pint.git",
                "reference": "c44bffbb2334e90fba560933c45948fa4a3f3e86"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/pint/zipball/c44bffbb2334e90fba560933c45948fa4a3f3e86",
                "reference": "c44bffbb2334e90fba560933c45948fa4a3f3e86",
                "shasum": ""
            },
            "require": {
                "ext-json": "*",
                "ext-mbstring": "*",
                "ext-tokenizer": "*",
                "ext-xml": "*",
                "php": "^8.2.0"
            },
            "require-dev": {
                "friendsofphp/php-cs-fixer": "^3.70.2",
                "illuminate/view": "^11.44.1",
                "larastan/larastan": "^3.1.0",
                "laravel-zero/framework": "^11.36.1",
                "mockery/mockery": "^1.6.12",
                "nunomaduro/termwind": "^2.3",
                "pestphp/pest": "^2.36.0"
            },
            "bin": [
                "builds/pint"
            ],
            "type": "project",
            "autoload": {
                "psr-4": {
                    "App\\": "app/",
                    "Database\\Seeders\\": "database/seeders/",
                    "Database\\Factories\\": "database/factories/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Nuno Maduro",
                    "email": "<EMAIL>"
                }
            ],
            "description": "An opinionated code formatter for PHP.",
            "homepage": "https://laravel.com",
            "keywords": [
                "format",
                "formatter",
                "lint",
                "linter",
                "php"
            ],
            "support": {
                "issues": "https://github.com/laravel/pint/issues",
                "source": "https://github.com/laravel/pint"
            },
            "time": "2025-03-11T03:22:21+00:00"
        },
        {
            "name": "laravel/sail",
            "version": "v1.43.0",
            "source": {
                "type": "git",
                "url": "https://github.com/laravel/sail.git",
                "reference": "71a509b14b2621ce58574274a74290f933c687f7"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/laravel/sail/zipball/71a509b14b2621ce58574274a74290f933c687f7",
                "reference": "71a509b14b2621ce58574274a74290f933c687f7",
                "shasum": ""
            },
            "require": {
                "illuminate/console": "^9.52.16|^10.0|^11.0|^12.0",
                "illuminate/contracts": "^9.52.16|^10.0|^11.0|^12.0",
                "illuminate/support": "^9.52.16|^10.0|^11.0|^12.0",
                "php": "^8.0",
                "symfony/console": "^6.0|^7.0",
                "symfony/yaml": "^6.0|^7.0"
            },
            "require-dev": {
                "orchestra/testbench": "^7.0|^8.0|^9.0|^10.0",
                "phpstan/phpstan": "^1.10"
            },
            "bin": [
                "bin/sail"
            ],
            "type": "library",
            "extra": {
                "laravel": {
                    "providers": [
                        "Laravel\\Sail\\SailServiceProvider"
                    ]
                }
            },
            "autoload": {
                "psr-4": {
                    "Laravel\\Sail\\": "src/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "authors": [
                {
                    "name": "Taylor Otwell",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Docker files for running a basic Laravel application.",
            "keywords": [
                "docker",
                "laravel"
            ],
            "support": {
                "issues": "https://github.com/laravel/sail/issues",
                "source": "https://github.com/laravel/sail"
            },
            "time": "2025-05-13T13:34:34+00:00"
        },
        {
            "name": "mockery/mockery",
            "version": "1.6.12",
            "source": {
                "type": "git",
                "url": "https://github.com/mockery/mockery.git",
                "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699",
                "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699",
                "shasum": ""
            },
            "require": {
                "hamcrest/hamcrest-php": "^2.0.1",
                "lib-pcre": ">=7.0",
                "php": ">=7.3"
            },
            "conflict": {
                "phpunit/phpunit": "<8.0"
            },
            "require-dev": {
                "phpunit/phpunit": "^8.5 || ^9.6.17",
                "symplify/easy-coding-standard": "^12.1.14"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "library/helpers.php",
                    "library/Mockery.php"
                ],
                "psr-4": {
                    "Mockery\\": "library/Mockery"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Pádraic Brady",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/padraic",
                    "role": "Author"
                },
                {
                    "name": "Dave Marshall",
                    "email": "<EMAIL>",
                    "homepage": "https://davedevelopment.co.uk",
                    "role": "Developer"
                },
                {
                    "name": "Nathanael Esayeas",
                    "email": "<EMAIL>",
                    "homepage": "https://github.com/ghostwriter",
                    "role": "Lead Developer"
                }
            ],
            "description": "Mockery is a simple yet flexible PHP mock object framework",
            "homepage": "https://github.com/mockery/mockery",
            "keywords": [
                "BDD",
                "TDD",
                "library",
                "mock",
                "mock objects",
                "mockery",
                "stub",
                "test",
                "test double",
                "testing"
            ],
            "support": {
                "docs": "https://docs.mockery.io/",
                "issues": "https://github.com/mockery/mockery/issues",
                "rss": "https://github.com/mockery/mockery/releases.atom",
                "security": "https://github.com/mockery/mockery/security/advisories",
                "source": "https://github.com/mockery/mockery"
            },
            "time": "2024-05-16T03:13:13+00:00"
        },
        {
            "name": "myclabs/deep-copy",
            "version": "1.13.0",
            "source": {
                "type": "git",
                "url": "https://github.com/myclabs/DeepCopy.git",
                "reference": "024473a478be9df5fdaca2c793f2232fe788e414"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/024473a478be9df5fdaca2c793f2232fe788e414",
                "reference": "024473a478be9df5fdaca2c793f2232fe788e414",
                "shasum": ""
            },
            "require": {
                "php": "^7.1 || ^8.0"
            },
            "conflict": {
                "doctrine/collections": "<1.6.8",
                "doctrine/common": "<2.13.3 || >=3 <3.2.2"
            },
            "require-dev": {
                "doctrine/collections": "^1.6.8",
                "doctrine/common": "^2.13.3 || ^3.2.2",
                "phpspec/prophecy": "^1.10",
                "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"
            },
            "type": "library",
            "autoload": {
                "files": [
                    "src/DeepCopy/deep_copy.php"
                ],
                "psr-4": {
                    "DeepCopy\\": "src/DeepCopy/"
                }
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "description": "Create deep copies (clones) of your objects",
            "keywords": [
                "clone",
                "copy",
                "duplicate",
                "object",
                "object graph"
            ],
            "support": {
                "issues": "https://github.com/myclabs/DeepCopy/issues",
                "source": "https://github.com/myclabs/DeepCopy/tree/1.13.0"
            },
            "funding": [
                {
                    "url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy",
                    "type": "tidelift"
                }
            ],
            "time": "2025-02-12T12:17:51+00:00"
        },
        {
            "name": "phar-io/manifest",
            "version": "2.0.4",
            "source": {
                "type": "git",
                "url": "https://github.com/phar-io/manifest.git",
                "reference": "54750ef60c58e43759730615a392c31c80e23176"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176",
                "reference": "54750ef60c58e43759730615a392c31c80e23176",
                "shasum": ""
            },
            "require": {
                "ext-dom": "*",
                "ext-libxml": "*",
                "ext-phar": "*",
                "ext-xmlwriter": "*",
                "phar-io/version": "^3.0.1",
                "php": "^7.2 || ^8.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-master": "2.0.x-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Arne Blankerts",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Sebastian Heuer",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)",
            "support": {
                "issues": "https://github.com/phar-io/manifest/issues",
                "source": "https://github.com/phar-io/manifest/tree/2.0.4"
            },
            "funding": [
                {
                    "url": "https://github.com/theseer",
                    "type": "github"
                }
            ],
            "time": "2024-03-03T12:33:53+00:00"
        },
        {
            "name": "phar-io/version",
            "version": "3.2.1",
            "source": {
                "type": "git",
                "url": "https://github.com/phar-io/version.git",
                "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74",
                "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74",
                "shasum": ""
            },
            "require": {
                "php": "^7.2 || ^8.0"
            },
            "type": "library",
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Arne Blankerts",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Sebastian Heuer",
                    "email": "<EMAIL>",
                    "role": "Developer"
                },
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "Library for handling version information and constraints",
            "support": {
                "issues": "https://github.com/phar-io/version/issues",
                "source": "https://github.com/phar-io/version/tree/3.2.1"
            },
            "time": "2022-02-21T01:04:05+00:00"
        },
        {
            "name": "phpunit/php-code-coverage",
            "version": "11.0.9",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/php-code-coverage.git",
                "reference": "14d63fbcca18457e49c6f8bebaa91a87e8e188d7"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/php-code-coverage/zipball/14d63fbcca18457e49c6f8bebaa91a87e8e188d7",
                "reference": "14d63fbcca18457e49c6f8bebaa91a87e8e188d7",
                "shasum": ""
            },
            "require": {
                "ext-dom": "*",
                "ext-libxml": "*",
                "ext-xmlwriter": "*",
                "nikic/php-parser": "^5.4.0",
                "php": ">=8.2",
                "phpunit/php-file-iterator": "^5.1.0",
                "phpunit/php-text-template": "^4.0.1",
                "sebastian/code-unit-reverse-lookup": "^4.0.1",
                "sebastian/complexity": "^4.0.1",
                "sebastian/environment": "^7.2.0",
                "sebastian/lines-of-code": "^3.0.1",
                "sebastian/version": "^5.0.2",
                "theseer/tokenizer": "^1.2.3"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.5.2"
            },
            "suggest": {
                "ext-pcov": "PHP extension that provides line coverage",
                "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "11.0.x-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.",
            "homepage": "https://github.com/sebastianbergmann/php-code-coverage",
            "keywords": [
                "coverage",
                "testing",
                "xunit"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/php-code-coverage/issues",
                "security": "https://github.com/sebastianbergmann/php-code-coverage/security/policy",
                "source": "https://github.com/sebastianbergmann/php-code-coverage/tree/11.0.9"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2025-02-25T13:26:39+00:00"
        },
        {
            "name": "phpunit/php-file-iterator",
            "version": "5.1.0",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/php-file-iterator.git",
                "reference": "118cfaaa8bc5aef3287bf315b6060b1174754af6"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/php-file-iterator/zipball/118cfaaa8bc5aef3287bf315b6060b1174754af6",
                "reference": "118cfaaa8bc5aef3287bf315b6060b1174754af6",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "5.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "FilterIterator implementation that filters files based on a list of suffixes.",
            "homepage": "https://github.com/sebastianbergmann/php-file-iterator/",
            "keywords": [
                "filesystem",
                "iterator"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/php-file-iterator/issues",
                "security": "https://github.com/sebastianbergmann/php-file-iterator/security/policy",
                "source": "https://github.com/sebastianbergmann/php-file-iterator/tree/5.1.0"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-08-27T05:02:59+00:00"
        },
        {
            "name": "phpunit/php-invoker",
            "version": "5.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/php-invoker.git",
                "reference": "c1ca3814734c07492b3d4c5f794f4b0995333da2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/php-invoker/zipball/c1ca3814734c07492b3d4c5f794f4b0995333da2",
                "reference": "c1ca3814734c07492b3d4c5f794f4b0995333da2",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "ext-pcntl": "*",
                "phpunit/phpunit": "^11.0"
            },
            "suggest": {
                "ext-pcntl": "*"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "5.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Invoke callables with a timeout",
            "homepage": "https://github.com/sebastianbergmann/php-invoker/",
            "keywords": [
                "process"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/php-invoker/issues",
                "security": "https://github.com/sebastianbergmann/php-invoker/security/policy",
                "source": "https://github.com/sebastianbergmann/php-invoker/tree/5.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T05:07:44+00:00"
        },
        {
            "name": "phpunit/php-text-template",
            "version": "4.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/php-text-template.git",
                "reference": "3e0404dc6b300e6bf56415467ebcb3fe4f33e964"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/php-text-template/zipball/3e0404dc6b300e6bf56415467ebcb3fe4f33e964",
                "reference": "3e0404dc6b300e6bf56415467ebcb3fe4f33e964",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "4.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Simple template engine.",
            "homepage": "https://github.com/sebastianbergmann/php-text-template/",
            "keywords": [
                "template"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/php-text-template/issues",
                "security": "https://github.com/sebastianbergmann/php-text-template/security/policy",
                "source": "https://github.com/sebastianbergmann/php-text-template/tree/4.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T05:08:43+00:00"
        },
        {
            "name": "phpunit/php-timer",
            "version": "7.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/php-timer.git",
                "reference": "3b415def83fbcb41f991d9ebf16ae4ad8b7837b3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/php-timer/zipball/3b415def83fbcb41f991d9ebf16ae4ad8b7837b3",
                "reference": "3b415def83fbcb41f991d9ebf16ae4ad8b7837b3",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "7.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Utility class for timing",
            "homepage": "https://github.com/sebastianbergmann/php-timer/",
            "keywords": [
                "timer"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/php-timer/issues",
                "security": "https://github.com/sebastianbergmann/php-timer/security/policy",
                "source": "https://github.com/sebastianbergmann/php-timer/tree/7.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T05:09:35+00:00"
        },
        {
            "name": "phpunit/phpunit",
            "version": "11.5.12",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/phpunit.git",
                "reference": "d42785840519401ed2113292263795eb4c0f95da"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/phpunit/zipball/d42785840519401ed2113292263795eb4c0f95da",
                "reference": "d42785840519401ed2113292263795eb4c0f95da",
                "shasum": ""
            },
            "require": {
                "ext-dom": "*",
                "ext-json": "*",
                "ext-libxml": "*",
                "ext-mbstring": "*",
                "ext-xml": "*",
                "ext-xmlwriter": "*",
                "myclabs/deep-copy": "^1.13.0",
                "phar-io/manifest": "^2.0.4",
                "phar-io/version": "^3.2.1",
                "php": ">=8.2",
                "phpunit/php-code-coverage": "^11.0.9",
                "phpunit/php-file-iterator": "^5.1.0",
                "phpunit/php-invoker": "^5.0.1",
                "phpunit/php-text-template": "^4.0.1",
                "phpunit/php-timer": "^7.0.1",
                "sebastian/cli-parser": "^3.0.2",
                "sebastian/code-unit": "^3.0.2",
                "sebastian/comparator": "^6.3.1",
                "sebastian/diff": "^6.0.2",
                "sebastian/environment": "^7.2.0",
                "sebastian/exporter": "^6.3.0",
                "sebastian/global-state": "^7.0.2",
                "sebastian/object-enumerator": "^6.0.1",
                "sebastian/type": "^5.1.0",
                "sebastian/version": "^5.0.2",
                "staabm/side-effects-detector": "^1.0.5"
            },
            "suggest": {
                "ext-soap": "To be able to generate mocks based on WSDL files"
            },
            "bin": [
                "phpunit"
            ],
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "11.5-dev"
                }
            },
            "autoload": {
                "files": [
                    "src/Framework/Assert/Functions.php"
                ],
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "The PHP Unit Testing framework.",
            "homepage": "https://phpunit.de/",
            "keywords": [
                "phpunit",
                "testing",
                "xunit"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/phpunit/issues",
                "security": "https://github.com/sebastianbergmann/phpunit/security/policy",
                "source": "https://github.com/sebastianbergmann/phpunit/tree/11.5.12"
            },
            "funding": [
                {
                    "url": "https://phpunit.de/sponsors.html",
                    "type": "custom"
                },
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                },
                {
                    "url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit",
                    "type": "tidelift"
                }
            ],
            "time": "2025-03-07T07:31:03+00:00"
        },
        {
            "name": "sebastian/cli-parser",
            "version": "3.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/cli-parser.git",
                "reference": "15c5dd40dc4f38794d383bb95465193f5e0ae180"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/cli-parser/zipball/15c5dd40dc4f38794d383bb95465193f5e0ae180",
                "reference": "15c5dd40dc4f38794d383bb95465193f5e0ae180",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "3.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Library for parsing CLI options",
            "homepage": "https://github.com/sebastianbergmann/cli-parser",
            "support": {
                "issues": "https://github.com/sebastianbergmann/cli-parser/issues",
                "security": "https://github.com/sebastianbergmann/cli-parser/security/policy",
                "source": "https://github.com/sebastianbergmann/cli-parser/tree/3.0.2"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:41:36+00:00"
        },
        {
            "name": "sebastian/code-unit",
            "version": "3.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/code-unit.git",
                "reference": "ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/code-unit/zipball/ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca",
                "reference": "ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.5"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "3.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Collection of value objects that represent the PHP code units",
            "homepage": "https://github.com/sebastianbergmann/code-unit",
            "support": {
                "issues": "https://github.com/sebastianbergmann/code-unit/issues",
                "security": "https://github.com/sebastianbergmann/code-unit/security/policy",
                "source": "https://github.com/sebastianbergmann/code-unit/tree/3.0.2"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-12-12T09:59:06+00:00"
        },
        {
            "name": "sebastian/code-unit-reverse-lookup",
            "version": "4.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/code-unit-reverse-lookup.git",
                "reference": "183a9b2632194febd219bb9246eee421dad8d45e"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/code-unit-reverse-lookup/zipball/183a9b2632194febd219bb9246eee421dad8d45e",
                "reference": "183a9b2632194febd219bb9246eee421dad8d45e",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "4.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Looks up which function or method a line of code belongs to",
            "homepage": "https://github.com/sebastianbergmann/code-unit-reverse-lookup/",
            "support": {
                "issues": "https://github.com/sebastianbergmann/code-unit-reverse-lookup/issues",
                "security": "https://github.com/sebastianbergmann/code-unit-reverse-lookup/security/policy",
                "source": "https://github.com/sebastianbergmann/code-unit-reverse-lookup/tree/4.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:45:54+00:00"
        },
        {
            "name": "sebastian/comparator",
            "version": "6.3.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/comparator.git",
                "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/comparator/zipball/24b8fbc2c8e201bb1308e7b05148d6ab393b6959",
                "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959",
                "shasum": ""
            },
            "require": {
                "ext-dom": "*",
                "ext-mbstring": "*",
                "php": ">=8.2",
                "sebastian/diff": "^6.0",
                "sebastian/exporter": "^6.0"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.4"
            },
            "suggest": {
                "ext-bcmath": "For comparing BcMath\\Number objects"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "6.3-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jeff Welch",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Volker Dusch",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Bernhard Schussek",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Provides the functionality to compare PHP values for equality",
            "homepage": "https://github.com/sebastianbergmann/comparator",
            "keywords": [
                "comparator",
                "compare",
                "equality"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/comparator/issues",
                "security": "https://github.com/sebastianbergmann/comparator/security/policy",
                "source": "https://github.com/sebastianbergmann/comparator/tree/6.3.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2025-03-07T06:57:01+00:00"
        },
        {
            "name": "sebastian/complexity",
            "version": "4.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/complexity.git",
                "reference": "ee41d384ab1906c68852636b6de493846e13e5a0"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/complexity/zipball/ee41d384ab1906c68852636b6de493846e13e5a0",
                "reference": "ee41d384ab1906c68852636b6de493846e13e5a0",
                "shasum": ""
            },
            "require": {
                "nikic/php-parser": "^5.0",
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "4.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Library for calculating the complexity of PHP code units",
            "homepage": "https://github.com/sebastianbergmann/complexity",
            "support": {
                "issues": "https://github.com/sebastianbergmann/complexity/issues",
                "security": "https://github.com/sebastianbergmann/complexity/security/policy",
                "source": "https://github.com/sebastianbergmann/complexity/tree/4.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:49:50+00:00"
        },
        {
            "name": "sebastian/diff",
            "version": "6.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/diff.git",
                "reference": "b4ccd857127db5d41a5b676f24b51371d76d8544"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/diff/zipball/b4ccd857127db5d41a5b676f24b51371d76d8544",
                "reference": "b4ccd857127db5d41a5b676f24b51371d76d8544",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0",
                "symfony/process": "^4.2 || ^5"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "6.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Kore Nordmann",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Diff implementation",
            "homepage": "https://github.com/sebastianbergmann/diff",
            "keywords": [
                "diff",
                "udiff",
                "unidiff",
                "unified diff"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/diff/issues",
                "security": "https://github.com/sebastianbergmann/diff/security/policy",
                "source": "https://github.com/sebastianbergmann/diff/tree/6.0.2"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:53:05+00:00"
        },
        {
            "name": "sebastian/environment",
            "version": "7.2.0",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/environment.git",
                "reference": "855f3ae0ab316bbafe1ba4e16e9f3c078d24a0c5"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/855f3ae0ab316bbafe1ba4e16e9f3c078d24a0c5",
                "reference": "855f3ae0ab316bbafe1ba4e16e9f3c078d24a0c5",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "suggest": {
                "ext-posix": "*"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "7.2-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Provides functionality to handle HHVM/PHP environments",
            "homepage": "https://github.com/sebastianbergmann/environment",
            "keywords": [
                "Xdebug",
                "environment",
                "hhvm"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/environment/issues",
                "security": "https://github.com/sebastianbergmann/environment/security/policy",
                "source": "https://github.com/sebastianbergmann/environment/tree/7.2.0"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:54:44+00:00"
        },
        {
            "name": "sebastian/exporter",
            "version": "6.3.0",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/exporter.git",
                "reference": "3473f61172093b2da7de1fb5782e1f24cc036dc3"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/exporter/zipball/3473f61172093b2da7de1fb5782e1f24cc036dc3",
                "reference": "3473f61172093b2da7de1fb5782e1f24cc036dc3",
                "shasum": ""
            },
            "require": {
                "ext-mbstring": "*",
                "php": ">=8.2",
                "sebastian/recursion-context": "^6.0"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.3"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "6.1-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jeff Welch",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Volker Dusch",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Adam Harvey",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Bernhard Schussek",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Provides the functionality to export PHP variables for visualization",
            "homepage": "https://www.github.com/sebastianbergmann/exporter",
            "keywords": [
                "export",
                "exporter"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/exporter/issues",
                "security": "https://github.com/sebastianbergmann/exporter/security/policy",
                "source": "https://github.com/sebastianbergmann/exporter/tree/6.3.0"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-12-05T09:17:50+00:00"
        },
        {
            "name": "sebastian/global-state",
            "version": "7.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/global-state.git",
                "reference": "3be331570a721f9a4b5917f4209773de17f747d7"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/global-state/zipball/3be331570a721f9a4b5917f4209773de17f747d7",
                "reference": "3be331570a721f9a4b5917f4209773de17f747d7",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "sebastian/object-reflector": "^4.0",
                "sebastian/recursion-context": "^6.0"
            },
            "require-dev": {
                "ext-dom": "*",
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "7.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Snapshotting of global state",
            "homepage": "https://www.github.com/sebastianbergmann/global-state",
            "keywords": [
                "global state"
            ],
            "support": {
                "issues": "https://github.com/sebastianbergmann/global-state/issues",
                "security": "https://github.com/sebastianbergmann/global-state/security/policy",
                "source": "https://github.com/sebastianbergmann/global-state/tree/7.0.2"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:57:36+00:00"
        },
        {
            "name": "sebastian/lines-of-code",
            "version": "3.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/lines-of-code.git",
                "reference": "d36ad0d782e5756913e42ad87cb2890f4ffe467a"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/lines-of-code/zipball/d36ad0d782e5756913e42ad87cb2890f4ffe467a",
                "reference": "d36ad0d782e5756913e42ad87cb2890f4ffe467a",
                "shasum": ""
            },
            "require": {
                "nikic/php-parser": "^5.0",
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "3.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Library for counting the lines of code in PHP source code",
            "homepage": "https://github.com/sebastianbergmann/lines-of-code",
            "support": {
                "issues": "https://github.com/sebastianbergmann/lines-of-code/issues",
                "security": "https://github.com/sebastianbergmann/lines-of-code/security/policy",
                "source": "https://github.com/sebastianbergmann/lines-of-code/tree/3.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T04:58:38+00:00"
        },
        {
            "name": "sebastian/object-enumerator",
            "version": "6.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/object-enumerator.git",
                "reference": "f5b498e631a74204185071eb41f33f38d64608aa"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/object-enumerator/zipball/f5b498e631a74204185071eb41f33f38d64608aa",
                "reference": "f5b498e631a74204185071eb41f33f38d64608aa",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2",
                "sebastian/object-reflector": "^4.0",
                "sebastian/recursion-context": "^6.0"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "6.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Traverses array structures and object graphs to enumerate all referenced objects",
            "homepage": "https://github.com/sebastianbergmann/object-enumerator/",
            "support": {
                "issues": "https://github.com/sebastianbergmann/object-enumerator/issues",
                "security": "https://github.com/sebastianbergmann/object-enumerator/security/policy",
                "source": "https://github.com/sebastianbergmann/object-enumerator/tree/6.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T05:00:13+00:00"
        },
        {
            "name": "sebastian/object-reflector",
            "version": "4.0.1",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/object-reflector.git",
                "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/object-reflector/zipball/6e1a43b411b2ad34146dee7524cb13a068bb35f9",
                "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "4.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Allows reflection of object attributes, including inherited and non-public ones",
            "homepage": "https://github.com/sebastianbergmann/object-reflector/",
            "support": {
                "issues": "https://github.com/sebastianbergmann/object-reflector/issues",
                "security": "https://github.com/sebastianbergmann/object-reflector/security/policy",
                "source": "https://github.com/sebastianbergmann/object-reflector/tree/4.0.1"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T05:01:32+00:00"
        },
        {
            "name": "sebastian/recursion-context",
            "version": "6.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/recursion-context.git",
                "reference": "694d156164372abbd149a4b85ccda2e4670c0e16"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/recursion-context/zipball/694d156164372abbd149a4b85ccda2e4670c0e16",
                "reference": "694d156164372abbd149a4b85ccda2e4670c0e16",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.0"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "6.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Jeff Welch",
                    "email": "<EMAIL>"
                },
                {
                    "name": "Adam Harvey",
                    "email": "<EMAIL>"
                }
            ],
            "description": "Provides functionality to recursively process PHP variables",
            "homepage": "https://github.com/sebastianbergmann/recursion-context",
            "support": {
                "issues": "https://github.com/sebastianbergmann/recursion-context/issues",
                "security": "https://github.com/sebastianbergmann/recursion-context/security/policy",
                "source": "https://github.com/sebastianbergmann/recursion-context/tree/6.0.2"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-07-03T05:10:34+00:00"
        },
        {
            "name": "sebastian/type",
            "version": "5.1.0",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/type.git",
                "reference": "461b9c5da241511a2a0e8f240814fb23ce5c0aac"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/type/zipball/461b9c5da241511a2a0e8f240814fb23ce5c0aac",
                "reference": "461b9c5da241511a2a0e8f240814fb23ce5c0aac",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "require-dev": {
                "phpunit/phpunit": "^11.3"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "5.1-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Collection of value objects that represent the types of the PHP type system",
            "homepage": "https://github.com/sebastianbergmann/type",
            "support": {
                "issues": "https://github.com/sebastianbergmann/type/issues",
                "security": "https://github.com/sebastianbergmann/type/security/policy",
                "source": "https://github.com/sebastianbergmann/type/tree/5.1.0"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-09-17T13:12:04+00:00"
        },
        {
            "name": "sebastian/version",
            "version": "5.0.2",
            "source": {
                "type": "git",
                "url": "https://github.com/sebastianbergmann/version.git",
                "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/sebastianbergmann/version/zipball/c687e3387b99f5b03b6caa64c74b63e2936ff874",
                "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874",
                "shasum": ""
            },
            "require": {
                "php": ">=8.2"
            },
            "type": "library",
            "extra": {
                "branch-alias": {
                    "dev-main": "5.0-dev"
                }
            },
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Sebastian Bergmann",
                    "email": "<EMAIL>",
                    "role": "lead"
                }
            ],
            "description": "Library that helps with managing the version number of Git-hosted PHP projects",
            "homepage": "https://github.com/sebastianbergmann/version",
            "support": {
                "issues": "https://github.com/sebastianbergmann/version/issues",
                "security": "https://github.com/sebastianbergmann/version/security/policy",
                "source": "https://github.com/sebastianbergmann/version/tree/5.0.2"
            },
            "funding": [
                {
                    "url": "https://github.com/sebastianbergmann",
                    "type": "github"
                }
            ],
            "time": "2024-10-09T05:16:32+00:00"
        },
        {
            "name": "staabm/side-effects-detector",
            "version": "1.0.5",
            "source": {
                "type": "git",
                "url": "https://github.com/staabm/side-effects-detector.git",
                "reference": "d8334211a140ce329c13726d4a715adbddd0a163"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/d8334211a140ce329c13726d4a715adbddd0a163",
                "reference": "d8334211a140ce329c13726d4a715adbddd0a163",
                "shasum": ""
            },
            "require": {
                "ext-tokenizer": "*",
                "php": "^7.4 || ^8.0"
            },
            "require-dev": {
                "phpstan/extension-installer": "^1.4.3",
                "phpstan/phpstan": "^1.12.6",
                "phpunit/phpunit": "^9.6.21",
                "symfony/var-dumper": "^5.4.43",
                "tomasvotruba/type-coverage": "1.0.0",
                "tomasvotruba/unused-public": "1.0.0"
            },
            "type": "library",
            "autoload": {
                "classmap": [
                    "lib/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "MIT"
            ],
            "description": "A static analysis tool to detect side effects in PHP code",
            "keywords": [
                "static analysis"
            ],
            "support": {
                "issues": "https://github.com/staabm/side-effects-detector/issues",
                "source": "https://github.com/staabm/side-effects-detector/tree/1.0.5"
            },
            "funding": [
                {
                    "url": "https://github.com/staabm",
                    "type": "github"
                }
            ],
            "time": "2024-10-20T05:08:20+00:00"
        },
        {
            "name": "theseer/tokenizer",
            "version": "1.2.3",
            "source": {
                "type": "git",
                "url": "https://github.com/theseer/tokenizer.git",
                "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"
            },
            "dist": {
                "type": "zip",
                "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2",
                "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2",
                "shasum": ""
            },
            "require": {
                "ext-dom": "*",
                "ext-tokenizer": "*",
                "ext-xmlwriter": "*",
                "php": "^7.2 || ^8.0"
            },
            "type": "library",
            "autoload": {
                "classmap": [
                    "src/"
                ]
            },
            "notification-url": "https://packagist.org/downloads/",
            "license": [
                "BSD-3-Clause"
            ],
            "authors": [
                {
                    "name": "Arne Blankerts",
                    "email": "<EMAIL>",
                    "role": "Developer"
                }
            ],
            "description": "A small library for converting tokenized PHP source code into XML and potentially other formats",
            "support": {
                "issues": "https://github.com/theseer/tokenizer/issues",
                "source": "https://github.com/theseer/tokenizer/tree/1.2.3"
            },
            "funding": [
                {
                    "url": "https://github.com/theseer",
                    "type": "github"
                }
            ],
            "time": "2024-03-03T12:36:25+00:00"
        }
    ],
    "aliases": [],
    "minimum-stability": "stable",
    "stability-flags": {},
    "prefer-stable": true,
    "prefer-lowest": false,
    "platform": {
        "php": "^8.2"
    },
    "platform-dev": [],
    "plugin-api-version": "2.3.0"
}
