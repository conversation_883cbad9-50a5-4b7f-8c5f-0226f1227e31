<?php

namespace App\Services\Mobile;

use Carbon\CarbonImmutable;
use <PERSON><PERSON><PERSON>cci\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;

class SocialLoginService
{
    private Configuration $jwtConfig;
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $this->jwtConfig = Configuration::forSymmetricSigner(
            new Signer\Hmac\Sha256(),
            InMemory::plainText(config('services.apple.private_key')),
        );
    }

    public function generateAppleSecret()
    {
        $now = CarbonImmutable::now();
 
        $token = $this->jwtConfig->builder()
            ->issuedBy(config('services.apple.team_id'))
            ->issuedAt($now)
            ->expiresAt($now->addDays(180))
            ->permittedFor('https://appleid.apple.com')
            ->relatedTo(config('services.apple.client_id'))
            ->withHeader('kid', config('services.apple.key_id'))
            ->getToken($this->jwtConfig->signer(), $this->jwtConfig->signingKey());
 
        return $token->toString();
    }

}
